package com.xinghuo.project.plan.model.risk;

import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import com.xinghuo.common.base.model.Pagination;
import java.util.Date;

/**
 * 项目实际风险实例分页查询
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "项目实际风险实例分页查询")
public class ProjectInsRiskPagination extends Pagination {
    
    @Schema(description = "所属的实际项目ID")
    private String projectId;
    
    @Schema(description = "风险标题")
    private String title;
    
    @Schema(description = "风险分类ID")
    private String riskCategoryId;
    
    @Schema(description = "概率等级ID")
    private String probabilityLevelId;
    
    @Schema(description = "影响等级ID")
    private String impactLevelId;
    
    @Schema(description = "负责人用户ID")
    private String responseUserId;
    
    @Schema(description = "状态ID")
    private String statusId;
    
    @Schema(description = "最小风险评分")
    private Integer minRiskScore;
    
    @Schema(description = "最大风险评分")
    private Integer maxRiskScore;
    
    @Schema(description = "创建开始时间")
    private Date startTime;
    
    @Schema(description = "创建结束时间")
    private Date endTime;
    
    @Schema(description = "截止日期开始")
    private Date dueDateStart;
    
    @Schema(description = "截止日期结束")
    private Date dueDateEnd;
}