<template>
  <div class="contract-paycontract-management">
    <div class="page-header">
      <div class="flex items-center">
        <i class="icon-ym icon-ym-paycontract mr-2 text-lg"></i>
        <span class="text-base font-medium">关联付款合同</span>
      </div>
      <div class="flex items-center space-x-2">
        <a-button type="primary" @click="handleAdd" :disabled="!hasContract">
          <template #icon><Icon icon="ant-design:plus-outlined" /></template>
          新增付款合同
        </a-button>
        <a-button @click="handleRefresh">
          <template #icon><Icon icon="ant-design:reload-outlined" /></template>
          刷新
        </a-button>
      </div>
    </div>

    <!-- 无合同提示 -->
    <div v-if="!hasContract" class="no-contract-tip">
      <a-card class="no-contract-card">
        <a-empty image="https://gw.alipayobjects.com/zos/antfincdn/ZHrcdLPrvN/empty.svg" :image-style="{ height: '80px' }" description="">
          <template #description>
            <div class="empty-description">
              <h3>尚未创建收款合同</h3>
              <p>付款合同需要关联到收款合同，请先创建收款合同</p>
            </div>
          </template>
          <a-button type="primary" size="large" @click="goToContractInfo">
            <template #icon><Icon icon="ant-design:plus-outlined" /></template>
            创建收款合同
          </a-button>
        </a-empty>
      </a-card>
    </div>

    <div class="page-content" v-if="hasContract">
      <a-spin :spinning="loading">
        <!-- 付款合同统计 -->
        <div class="paycontract-summary mb-4">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-card>
                <a-statistic title="外采金额" :value="statistics.totalAmount" :precision="2" suffix="元" :value-style="{ color: '#1890ff' }" />
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card>
                <a-statistic title="已付金额" :value="statistics.paidAmount" :precision="2" suffix="元" :value-style="{ color: '#52c41a' }" />
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card>
                <a-statistic title="未付金额" :value="statistics.unpaidAmount" :precision="2" suffix="元" :value-style="{ color: '#f5222d' }" />
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card>
                <a-statistic title="付款进度" :value="statistics.paymentProgress" suffix="%" :precision="1" :value-style="{ color: '#faad14' }" />
              </a-card>
            </a-col>
          </a-row>
        </div>

        <!-- 付款合同列表 -->
        <div class="paycontract-list">
          <BasicTable @register="registerTable">
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <TableAction
                  :actions="[
                    {
                      label: '查看',
                      onClick: () => handleView(record),
                    },
                    {
                      label: '编辑',
                      onClick: () => handleEdit(record),
                    },
                    {
                      label: '删除',
                      color: 'error',
                      popConfirm: {
                        title: '确定要删除这个付款合同吗？',
                        confirm: () => handleDelete(record),
                      },
                    },
                  ]"
                />
              </template>
            </template>
          </BasicTable>
        </div>
      </a-spin>
    </div>

    <!-- 新增/编辑表单抽屉 -->
    <PaycontractForm @register="registerFormDrawer" @success="handleFormSuccess" />

    <!-- 详情查看模态框 -->
    <PaycontractDetail :visible="detailVisible" :record="currentRecord" @update:visible="detailVisible = $event" @cancel="handleDetailCancel" />
  </div>
</template>

<script lang="ts" setup>
  // 1. Vue 核心导入
  import { ref, onMounted, inject, reactive } from 'vue';

  // 2. 类型导入
  import type { PaymentContractVO, PaymentContractForm } from '/@/api/project/paymentContract';

  // 3. 第三方组件导入
  // (无第三方组件)

  // 4. 项目基础组件
  import { Icon } from '/@/components/Icon';
  import { useDrawer } from '/@/components/Drawer';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';

  // 5. 业务组件导入
  import PaycontractForm from './components/PaycontractForm.vue';
  import PaycontractDetail from './components/PaycontractDetail.vue';

  // 6. Hooks 导入
  import { useMessage } from '/@/hooks/web/useMessage';

  // 7. 工具函数导入
  import { formatToDate } from '/@/utils/dateUtil';

  // 8. API 导入
  import paymentContractApi from '/@/api/project/paymentContract';

  // 从父组件注入项目ID和合同信息
  const projectId = inject('projectId', ref(''));
  const contractId = inject('contractId', ref(''));
  const contractInfo = inject('contractInfo', ref(null));
  const hasContract = inject('hasContract', ref(false));

  const { createMessage } = useMessage();

  const loading = ref(false);
  const paycontractList = ref<PaymentContractVO[]>([]);

  // Form and modal states
  const detailVisible = ref(false);
  const currentRecord = ref<PaymentContractVO | null>(null);

  // 使用框架抽屉组件
  const [registerFormDrawer, { openDrawer: openFormDrawer }] = useDrawer();

  // Statistics
  const statistics = ref({
    totalAmount: 0,
    paidAmount: 0,
    unpaidAmount: 0,
    paymentProgress: 0,
  });

  // 表格列定义 - 采购合同字段
  const getTableColumns = () => [
    {
      title: '采购合同名称',
      dataIndex: 'name',
      width: 200,
    },
    {
      title: '采购合同编号',
      dataIndex: 'cno',
      width: 150,
    },
    {
      title: '供应商',
      dataIndex: 'supplierName',
      width: 150,
    },
    {
      title: '负责人',
      dataIndex: 'ownName',
      width: 100,
    },
    {
      title: '合同金额',
      dataIndex: 'amount',
      width: 120,
      customRender: ({ text }) => `¥${formatMoney(text)}`,
    },
    {
      title: '已付金额',
      dataIndex: 'yfAmount',
      width: 120,
      customRender: ({ text }) => `¥${formatMoney(text)}`,
    },
    {
      title: '合同状态',
      dataIndex: 'status',
      width: 100,
      customRender: ({ text }) => getPayStatusText(text),
    },
    {
      title: '签订日期',
      dataIndex: 'signDate',
      width: 120,
      customRender: ({ text }) => formatDate(text),
    },
  ];

  // BasicTable 数据加载函数
  const loadPaycontractData = async (params: any) => {
    if (!contractId.value) {
      console.log('⚠️ 合同ID为空，无法加载关联的采购合同');
      return { items: [], total: 0 };
    }

    try {
      console.log('🔍 [分页加载] 开始加载采购合同数据');
      console.log('🔍 [分页加载] 合同ID:', contractId.value);
      console.log('🔍 [分页加载] 分页参数:', params);

      // 构建分页查询参数
      const pagination = {
        currentPage: params.page || 1,
        pageSize: params.pageSize || 10,
        keyword: params.keyword || '', // 搜索关键字
      };

      console.log('🔍 [分页加载] 最终查询参数:', pagination);

      // 调用支持分页的API
      const response = await paymentContractApi.getByContractIdWithPage(contractId.value, pagination);

      if ((response as any).code === 200) {
        const data = (response as any).data?.list || [];
        const total = (response as any).data?.pagination?.total || 0;

        console.log('✅ [分页加载] 采购合同加载成功:', data.length, '条记录，总计:', total);
        console.log('🔍 [调试] 完整响应数据:', response);
        console.log('🔍 [调试] 数据列表:', data);

        // 加载统计数据（使用后台接口）
        loadStatistics();

        // BasicTable 期望直接返回响应数据，而不是包装格式
        console.log('🔍 [调试] 原始响应数据:', response);
        console.log('🔍 [调试] 数据列表:', data);
        console.log('🔍 [调试] 数据项数量:', data.length);
        console.log('🔍 [调试] 第一条数据:', data[0]);

        // 直接返回 API 响应，让 BasicTable 自己处理
        return response;
      } else {
        createMessage.error((response as any).msg || '加载关联采购合同失败');
        return { items: [], total: 0 };
      }
    } catch (error) {
      console.error('❌ [分页加载] 加载关联采购合同失败:', error);
      createMessage.error('加载关联采购合同失败');
      return { items: [], total: 0 };
    }
  };

  // 配置 BasicTable
  const [registerTable, { reload: reloadTable }] = useTable({
    title: '付款合同列表',
    api: loadPaycontractData,
    columns: getTableColumns(),
    useSearchForm: true,
    showTableSetting: true,
    bordered: true,
    showIndexColumn: false,
    // 配置数据提取路径（只需要修改与默认不同的字段）
    // fetchSetting: {
    //   listField: 'data.list',  // 默认是 'list'，我们的是 'data.list'
    //   // totalField 默认就是 'pagination.total'，正好匹配我们的 'data.pagination.total'
    // },
    formConfig: {
      labelWidth: 80,
      schemas: [
        {
          field: 'keyword',
          label: '关键字',
          component: 'Input',
          componentProps: {
            placeholder: '请输入合同名称或编号',
          },
          colProps: { span: 8 },
        },
      ],
    },
    actionColumn: {
      width: 180,
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
    },
  });

  // 格式化金额
  const formatMoney = (amount: number | string) => {
    if (!amount) return '0.00';
    return Number(amount).toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  // 格式化日期
  const formatDate = (date: string) => {
    return date ? formatToDate(date) : '-';
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      '1': 'orange',
      '2': 'blue',
      '3': 'green',
      '4': 'red',
    };
    return colorMap[status] || 'default';
  };

  // 获取合同状态文本
  const getContractStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      draft: '草稿',
      signed: '已签订',
      executing: '执行中',
      completed: '已完成',
      cancelled: '已取消',
      suspended: '已暂停',
    };
    return textMap[status] || '未知';
  };

  // 获取付款状态文本
  const getPayStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      // 数字状态值
      '1': '执行中',
      '2': '已完成',
      '3': '已取消',
      '0': '草稿',
      // 英文状态值（兼容）
      UNPAID: '未付款',
      PARTIAL_PAID: '部分付款',
      FULLY_PAID: '已付清',
      OVERDUE: '逾期',
      PENDING: '待付款',
      PROCESSING: '付款中',
      CANCELLED: '已取消',
    };
    return textMap[status] || `未知(${status})`;
  };

  // 加载统计数据（使用全量数据接口）
  const loadStatistics = async () => {
    if (!contractId.value) {
      console.log('⚠️ 合同ID为空，无法加载统计数据');
      return;
    }

    try {
      console.log('📊 [统计数据] 开始加载付款合同统计数据，合同ID:', contractId.value);

      // 由于后台统计接口尚未实现，使用全量数据接口计算统计
      const response = await paymentContractApi.getByContractId(contractId.value);

      if ((response as any).code === 200) {
        const data = (response as any).data || [];
        console.log('✅ [统计数据] 全量数据加载成功:', data.length, '条记录');

        // 计算统计数据
        const totalAmount = data.reduce((sum: number, item: any) => sum + (item.amount || 0), 0);
        const paidAmount = data.reduce((sum: number, item: any) => sum + (item.yfAmount || 0), 0);
        const unpaidAmount = totalAmount - paidAmount;
        const paymentProgress = totalAmount > 0 ? (paidAmount / totalAmount) * 100 : 0;

        // 更新统计数据
        statistics.value.totalAmount = totalAmount;
        statistics.value.paidAmount = paidAmount;
        statistics.value.unpaidAmount = unpaidAmount;
        statistics.value.paymentProgress = paymentProgress;

        console.log('📊 [统计数据] 更新后的统计信息:', statistics.value);
      } else {
        console.error('❌ [统计数据] 获取全量数据失败:', (response as any).msg);
      }
    } catch (error) {
      console.error('❌ [统计数据] 加载统计数据异常:', error);
    }
  };

  // 新增付款合同
  const handleAdd = () => {
    if (!contractId.value) {
      createMessage.warning({
        content: '当前项目尚未创建收款合同，请先创建收款合同',
        duration: 3,
      });
      return;
    }
    openFormDrawer(true, {
      isUpdate: false,
      contractId: contractId.value,
      contractInfo: contractInfo.value,
      record: null,
    });
  };

  // 查看付款合同
  const handleView = (record: PaymentContractVO) => {
    currentRecord.value = record;
    detailVisible.value = true;
  };

  // 编辑付款合同
  const handleEdit = (record: PaymentContractVO) => {
    openFormDrawer(true, {
      isUpdate: true,
      contractId: contractId.value,
      contractInfo: contractInfo.value,
      record: record,
    });
  };

  // 删除付款合同
  const handleDelete = async (record: PaymentContractVO) => {
    try {
      const response = await paymentContractApi.delete(record.id);
      if ((response as any).code === 200) {
        createMessage.success('删除成功');
        reloadTable();
        loadStatistics(); // 重新加载统计数据
      } else {
        createMessage.error((response as any).msg || '删除失败');
      }
    } catch (error) {
      console.error('删除付款合同失败:', error);
      createMessage.error('删除失败');
    }
  };

  // 表单提交成功回调
  const handleFormSuccess = () => {
    reloadTable();
    loadStatistics(); // 重新加载统计数据
  };

  // 关闭详情模态框
  const handleDetailCancel = () => {
    detailVisible.value = false;
    currentRecord.value = null;
  };

  // 跳转到合同信息页面
  const goToContractInfo = () => {
    // 发送事件给父组件，让父组件切换到合同信息页面
    // 这样可以保持在同一个项目概览页面内，只是切换标签页
    const event = new CustomEvent('switchToContractInfo', {
      bubbles: true,
      detail: { targetTab: 'contractManagement', targetSubTab: 'info' },
    });
    document.dispatchEvent(event);

    // 如果上面的事件方式不工作，使用简单的提示
    createMessage.info('请点击左侧菜单中的"合同信息"来创建收款合同');
  };

  // 刷新
  const handleRefresh = () => {
    reloadTable();
  };

  onMounted(() => {
    // BasicTable 会自动加载数据，不需要手动调用
    // 但需要手动加载统计数据
    loadStatistics();
  });
</script>

<style lang="less" scoped>
  .contract-paycontract-management {
    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 16px;
      background: #fff;
      border-radius: 6px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    }

    .no-contract-tip {
      margin-bottom: 16px;

      .no-contract-card {
        text-align: center;
        padding: 40px 20px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);

        .empty-description {
          margin-bottom: 24px;

          h3 {
            color: #262626;
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 8px;
          }

          p {
            color: #8c8c8c;
            font-size: 14px;
            margin: 0;
          }
        }
      }
    }

    .page-content {
      .paycontract-summary {
        .ant-card {
          text-align: center;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
        }
      }

      .paycontract-list {
        .ant-card {
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
        }
      }
    }
  }
</style>
