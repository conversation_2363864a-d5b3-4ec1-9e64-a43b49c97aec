package com.xinghuo.project.schema.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目模板交付物计划实体类
 * 对应数据库表：zz_proj_schema_workproduct
 *
 * 项目模板的交付物计划配置，包含交付物基本信息和在模板中的特定配置。
 * 交付物可以从标准交付物库继承，但允许在模板中进行个性化覆盖。
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_schema_workproduct")
public class ProjectSchemaWorkproductEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 所属项目模板ID (关联 zz_proj_template)
     */
    @TableField("project_template_id")
    private String projectTemplateId;

    /**
     * 源自哪个标准交付物库ID (用于追溯)
     */
    @TableField("library_workproduct_id")
    private String libraryWorkproductId;

    /**
     * 交付物名称
     */
    @TableField("name")
    private String name;

    /**
     * 描述/验收标准
     */
    @TableField("description")
    private String description;

    /**
     * 交付物类型ID
     */
    @TableField("type_id")
    private String typeId;

    /**
     * 交付物子类型ID
     */
    @TableField("sub_type_id")
    private String subTypeId;

    /**
     * 显示顺序
     */
    @TableField("seq_no")
    private Integer seqNo;

    /**
     * 是否是必需交付物 (1:是, 0:否)
     */
    @TableField("is_required")
    private Integer isRequired;

    /**
     * 责任角色ID (可覆盖库中的default_role_id)
     */
    @TableField("response_role_id")
    private String responseRoleId;

    /**
     * 是否需要评审 (可覆盖库中的need_review)
     */
    @TableField("review_required")
    private Integer reviewRequired;

    /**
     * 关联的项目模板阶段ID
     */
    @TableField("schema_phase_id")
    private String schemaPhaseId;

    /**
     * 关联的项目模板WBS节点ID
     */
    @TableField("schema_wbs_id")
    private String schemaWbsId;
}