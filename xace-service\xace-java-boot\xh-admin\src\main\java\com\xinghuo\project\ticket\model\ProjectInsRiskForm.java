package com.xinghuo.project.ticket.model;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Date;

/**
 * 项目实际风险实例表单对象
 */
@Data
@Schema(description = "项目实际风险实例表单对象")
public class ProjectInsRiskForm {
    
    @Schema(description = "风险实例ID，新增时为空，更新时必填")
    private String id;
    
    @Schema(description = "所属的实际项目ID")
    @NotBlank(message = "项目ID不能为空")
    private String projectId;
    
    @Schema(description = "源自哪个标准风险库ID")
    private String sourceLibraryRiskId;
    
    @Schema(description = "风险标题")
    @NotBlank(message = "风险标题不能为空")
    private String title;
    
    @Schema(description = "风险描述")
    private String description;
    
    @Schema(description = "风险分类ID")
    @NotBlank(message = "风险分类不能为空")
    private String riskCategoryId;
    
    @Schema(description = "概率等级ID")
    private String probabilityLevelId;
    
    @Schema(description = "影响等级ID")
    private String impactLevelId;
    
    @Schema(description = "风险评分")
    private Integer riskScore;
    
    @Schema(description = "应对策略")
    private String strategy;
    
    @Schema(description = "应对措施")
    private String actions;
    
    @Schema(description = "负责人用户ID")
    private String responseUserId;
    
    @Schema(description = "截止日期")
    private Date dueDate;
    
    @Schema(description = "状态ID")
    @NotBlank(message = "状态不能为空")
    private String statusId;
    
    @Schema(description = "日志记录")
    private String log;
}