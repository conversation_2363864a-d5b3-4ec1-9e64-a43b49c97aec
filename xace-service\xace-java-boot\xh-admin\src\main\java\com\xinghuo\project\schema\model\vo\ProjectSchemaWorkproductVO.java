package com.xinghuo.project.schema.model.vo;

import com.xinghuo.project.schema.entity.ProjectSchemaWorkproductEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目模板交付物计划VO类
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectSchemaWorkproductVO extends ProjectSchemaWorkproductEntity {

    /**
     * 项目模板名称（冗余字段，便于显示）
     */
    private String projectTemplateName;

    /**
     * 交付物类型名称（冗余字段，便于显示）
     */
    private String typeName;

    /**
     * 交付物子类型名称（冗余字段，便于显示）
     */
    private String subTypeName;

    /**
     * 责任角色名称（冗余字段，便于显示）
     */
    private String responseRoleName;

    /**
     * 关联阶段名称（冗余字段，便于显示）
     */
    private String schemaPhaseName;

    /**
     * 关联WBS节点名称（冗余字段，便于显示）
     */
    private String schemaWbsName;

    /**
     * 标准交付物库名称（冗余字段，便于显示）
     */
    private String libraryWorkproductName;

    /**
     * 是否是必需交付物显示文本
     */
    private String isRequiredText;

    /**
     * 是否需要评审显示文本
     */
    private String reviewRequiredText;
}