package com.xinghuo.project.schema.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.project.schema.entity.ProjectSchemaWorkproductEntity;
import com.xinghuo.project.schema.model.ProjectSchemaWorkproductPagination;
import com.xinghuo.project.schema.model.vo.ProjectSchemaWorkproductVO;
import com.xinghuo.project.schema.service.ProjectSchemaWorkproductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import cn.dev33.satoken.annotation.SaCheckPermission;

import java.util.List;

/**
 * 项目模板交付物计划管理控制器
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Slf4j
@Tag(name = "项目模板交付物计划管理", description = "项目模板交付物计划管理相关接口")
@RestController
@RequestMapping("/api/project/schema/workproduct")
public class ProjectSchemaWorkproductController {

    @Resource
    private ProjectSchemaWorkproductService projectSchemaWorkproductService;

    /**
     * 获取项目模板交付物计划列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取项目模板交付物计划列表")
    @SaCheckPermission("project:schema:workproduct:view")
    public ActionResult<PageListVO<ProjectSchemaWorkproductVO>> list(@RequestBody ProjectSchemaWorkproductPagination pagination) {
        try {
            List<ProjectSchemaWorkproductVO> list = projectSchemaWorkproductService.getList(pagination);

            // 对结果进行数据转换和补充
            for (ProjectSchemaWorkproductVO vo : list) {
                // 是否必需字段转换
                if (vo.getIsRequired() != null) {
                    vo.setIsRequiredText(vo.getIsRequired() == 1 ? "是" : "否");
                }
                
                // 是否需要评审字段转换
                if (vo.getReviewRequired() != null) {
                    vo.setReviewRequiredText(vo.getReviewRequired() == 1 ? "是" : "否");
                }

                // TODO: 可以在这里添加其他关联数据的查询和设置
                // 例如：项目模板名称、类型名称、角色名称等
            }

            PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
            return ActionResult.page(list, page);
        } catch (Exception e) {
            log.error("获取项目模板交付物计划列表失败", e);
            return ActionResult.fail("获取项目模板交付物计划列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据项目模板ID获取交付物计划列表
     */
    @GetMapping("/getListByTemplateId/{projectTemplateId}")
    @Operation(summary = "根据项目模板ID获取交付物计划列表")
    @SaCheckPermission("project:schema:workproduct:view")
    public ActionResult<List<ProjectSchemaWorkproductEntity>> getListByProjectTemplateId(
            @Parameter(description = "项目模板ID") @PathVariable String projectTemplateId) {
        try {
            List<ProjectSchemaWorkproductEntity> list = projectSchemaWorkproductService.getListByProjectTemplateId(projectTemplateId);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据项目模板ID获取交付物计划列表失败", e);
            return ActionResult.fail("获取交付物计划列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据阶段ID获取交付物计划列表
     */
    @GetMapping("/getListByPhaseId/{schemaPhaseId}")
    @Operation(summary = "根据阶段ID获取交付物计划列表")
    @SaCheckPermission("project:schema:workproduct:view")
    public ActionResult<List<ProjectSchemaWorkproductEntity>> getListBySchemaPhaseId(
            @Parameter(description = "阶段ID") @PathVariable String schemaPhaseId) {
        try {
            List<ProjectSchemaWorkproductEntity> list = projectSchemaWorkproductService.getListBySchemaPhaseId(schemaPhaseId);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据阶段ID获取交付物计划列表失败", e);
            return ActionResult.fail("获取交付物计划列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据WBS节点ID获取交付物计划列表
     */
    @GetMapping("/getListByWbsId/{schemaWbsId}")
    @Operation(summary = "根据WBS节点ID获取交付物计划列表")
    @SaCheckPermission("project:schema:workproduct:view")
    public ActionResult<List<ProjectSchemaWorkproductEntity>> getListBySchemaWbsId(
            @Parameter(description = "WBS节点ID") @PathVariable String schemaWbsId) {
        try {
            List<ProjectSchemaWorkproductEntity> list = projectSchemaWorkproductService.getListBySchemaWbsId(schemaWbsId);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据WBS节点ID获取交付物计划列表失败", e);
            return ActionResult.fail("获取交付物计划列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取交付物计划详情
     */
    @GetMapping("/getInfo/{id}")
    @Operation(summary = "获取交付物计划详情")
    @SaCheckPermission("project:schema:workproduct:view")
    public ActionResult<ProjectSchemaWorkproductVO> getInfo(@Parameter(description = "交付物计划ID") @PathVariable String id) {
        try {
            ProjectSchemaWorkproductVO workproductVO = projectSchemaWorkproductService.getDetailInfo(id);
            
            // 数据转换
            if (workproductVO.getIsRequired() != null) {
                workproductVO.setIsRequiredText(workproductVO.getIsRequired() == 1 ? "是" : "否");
            }
            if (workproductVO.getReviewRequired() != null) {
                workproductVO.setReviewRequiredText(workproductVO.getReviewRequired() == 1 ? "是" : "否");
            }
            
            return ActionResult.success(workproductVO);
        } catch (Exception e) {
            log.error("获取交付物计划详情失败", e);
            return ActionResult.fail("获取交付物计划详情失败：" + e.getMessage());
        }
    }

    /**
     * 创建交付物计划
     */
    @PostMapping("/create")
    @Operation(summary = "创建交付物计划")
    @SaCheckPermission("project:schema:workproduct:add")
    public ActionResult<String> create(@Valid @RequestBody ProjectSchemaWorkproductVO workproductVO) {
        try {
            String id = projectSchemaWorkproductService.create(workproductVO);
            return ActionResult.success(id);
        } catch (Exception e) {
            log.error("创建交付物计划失败", e);
            return ActionResult.fail("创建交付物计划失败：" + e.getMessage());
        }
    }

    /**
     * 更新交付物计划
     */
    @PutMapping("/update/{id}")
    @Operation(summary = "更新交付物计划")
    @SaCheckPermission("project:schema:workproduct:edit")
    public ActionResult<Object> update(@Parameter(description = "交付物计划ID") @PathVariable String id,
                                      @Valid @RequestBody ProjectSchemaWorkproductVO workproductVO) {
        try {
            projectSchemaWorkproductService.update(id, workproductVO);
            return ActionResult.success("更新成功");
        } catch (Exception e) {
            log.error("更新交付物计划失败", e);
            return ActionResult.fail("更新交付物计划失败：" + e.getMessage());
        }
    }

    /**
     * 删除交付物计划
     */
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除交付物计划")
    @SaCheckPermission("project:schema:workproduct:delete")
    public ActionResult<Object> delete(@Parameter(description = "交付物计划ID") @PathVariable String id) {
        try {
            projectSchemaWorkproductService.delete(id);
            return ActionResult.success("删除成功");
        } catch (Exception e) {
            log.error("删除交付物计划失败", e);
            return ActionResult.fail("删除交付物计划失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除交付物计划
     */
    @DeleteMapping("/batchDelete")
    @Operation(summary = "批量删除交付物计划")
    @SaCheckPermission("project:schema:workproduct:delete")
    public ActionResult<Object> batchDelete(@RequestBody List<String> ids) {
        try {
            projectSchemaWorkproductService.batchDelete(ids);
            return ActionResult.success("批量删除成功");
        } catch (Exception e) {
            log.error("批量删除交付物计划失败", e);
            return ActionResult.fail("批量删除交付物计划失败：" + e.getMessage());
        }
    }

    /**
     * 更新必需状态
     */
    @PutMapping("/updateRequired/{id}/{isRequired}")
    @Operation(summary = "更新必需状态")
    @SaCheckPermission("project:schema:workproduct:edit")
    public ActionResult<Object> updateRequired(@Parameter(description = "交付物计划ID") @PathVariable String id,
                                              @Parameter(description = "是否必需") @PathVariable Integer isRequired) {
        try {
            projectSchemaWorkproductService.updateRequired(id, isRequired);
            return ActionResult.success("更新成功");
        } catch (Exception e) {
            log.error("更新必需状态失败", e);
            return ActionResult.fail("更新必需状态失败：" + e.getMessage());
        }
    }

    /**
     * 批量更新必需状态
     */
    @PutMapping("/batchUpdateRequired/{isRequired}")
    @Operation(summary = "批量更新必需状态")
    @SaCheckPermission("project:schema:workproduct:edit")
    public ActionResult<Object> batchUpdateRequired(@RequestBody List<String> ids,
                                                   @Parameter(description = "是否必需") @PathVariable Integer isRequired) {
        try {
            projectSchemaWorkproductService.batchUpdateRequired(ids, isRequired);
            return ActionResult.success("批量更新成功");
        } catch (Exception e) {
            log.error("批量更新必需状态失败", e);
            return ActionResult.fail("批量更新必需状态失败：" + e.getMessage());
        }
    }

    /**
     * 更新评审要求
     */
    @PutMapping("/updateReviewRequired/{id}/{reviewRequired}")
    @Operation(summary = "更新评审要求")
    @SaCheckPermission("project:schema:workproduct:edit")
    public ActionResult<Object> updateReviewRequired(@Parameter(description = "交付物计划ID") @PathVariable String id,
                                                    @Parameter(description = "是否需要评审") @PathVariable Integer reviewRequired) {
        try {
            projectSchemaWorkproductService.updateReviewRequired(id, reviewRequired);
            return ActionResult.success("更新成功");
        } catch (Exception e) {
            log.error("更新评审要求失败", e);
            return ActionResult.fail("更新评审要求失败：" + e.getMessage());
        }
    }

    /**
     * 从标准交付物库导入
     */
    @PostMapping("/importFromLibrary/{projectTemplateId}")
    @Operation(summary = "从标准交付物库导入")
    @SaCheckPermission("project:schema:workproduct:add")
    public ActionResult<Object> importFromLibrary(@Parameter(description = "项目模板ID") @PathVariable String projectTemplateId,
                                                 @RequestBody List<String> libraryWorkproductIds) {
        try {
            projectSchemaWorkproductService.importFromLibrary(projectTemplateId, libraryWorkproductIds);
            return ActionResult.success("导入成功");
        } catch (Exception e) {
            log.error("从标准交付物库导入失败", e);
            return ActionResult.fail("导入失败：" + e.getMessage());
        }
    }

    /**
     * 复制交付物计划
     */
    @PostMapping("/copyWorkproducts/{sourceProjectTemplateId}/{targetProjectTemplateId}")
    @Operation(summary = "复制交付物计划")
    @SaCheckPermission("project:schema:workproduct:add")
    public ActionResult<Object> copyWorkproducts(@Parameter(description = "源项目模板ID") @PathVariable String sourceProjectTemplateId,
                                                @Parameter(description = "目标项目模板ID") @PathVariable String targetProjectTemplateId) {
        try {
            projectSchemaWorkproductService.copyWorkproducts(sourceProjectTemplateId, targetProjectTemplateId);
            return ActionResult.success("复制成功");
        } catch (Exception e) {
            log.error("复制交付物计划失败", e);
            return ActionResult.fail("复制失败：" + e.getMessage());
        }
    }

    /**
     * 批量调整显示顺序
     */
    @PutMapping("/batchUpdateSeqNo")
    @Operation(summary = "批量调整显示顺序")
    @SaCheckPermission("project:schema:workproduct:edit")
    public ActionResult<Object> batchUpdateSeqNo(@RequestBody List<String> workproductIds) {
        try {
            projectSchemaWorkproductService.batchUpdateSeqNo(workproductIds);
            return ActionResult.success("调整顺序成功");
        } catch (Exception e) {
            log.error("批量调整显示顺序失败", e);
            return ActionResult.fail("调整顺序失败：" + e.getMessage());
        }
    }
}