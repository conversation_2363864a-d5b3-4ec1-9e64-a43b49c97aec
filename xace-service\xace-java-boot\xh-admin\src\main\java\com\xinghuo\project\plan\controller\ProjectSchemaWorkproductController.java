package com.xinghuo.project.plan.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.project.plan.entity.ProjectSchemaWorkproductEntity;
import com.xinghuo.project.plan.model.workproduct.ProjectSchemaWorkproductForm;
import com.xinghuo.project.plan.model.workproduct.ProjectSchemaWorkproductPagination;
import com.xinghuo.project.plan.model.workproduct.ProjectSchemaWorkproductVO;
import com.xinghuo.project.plan.service.ProjectSchemaWorkproductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 项目模板交付物计划管理控制器
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Slf4j
@Tag(name = "项目模板交付物计划管理", description = "项目模板交付物计划管理相关接口")
@RestController
@RequestMapping("/api/project/plan/projectSchemaWorkproduct")
public class ProjectSchemaWorkproductController {

    @Resource
    private ProjectSchemaWorkproductService projectSchemaWorkproductService;

    /**
     * 获取交付物计划列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取交付物计划列表")
    public ActionResult<PageListVO<ProjectSchemaWorkproductVO>> list(@RequestBody ProjectSchemaWorkproductPagination pagination) {
        try {
            List<ProjectSchemaWorkproductEntity> list = projectSchemaWorkproductService.getList(pagination);
            List<ProjectSchemaWorkproductVO> listVO = BeanCopierUtils.copyList(list, ProjectSchemaWorkproductVO.class);

            // 对结果进行数据转换和补充
            for (ProjectSchemaWorkproductVO vo : listVO) {
                // 是否必需名称转换
                vo.setIsRequiredName(vo.getIsRequired() != null && vo.getIsRequired() == 1 ? "是" : "否");
                // 是否需要评审名称转换
                vo.setReviewRequiredName(vo.getReviewRequired() != null && vo.getReviewRequired() == 1 ? "是" : "否");
            }

            PageListVO<ProjectSchemaWorkproductVO> vo = new PageListVO<>();
            vo.setList(listVO);
            return ActionResult.success(vo);
        } catch (Exception e) {
            log.error("获取交付物计划列表失败", e);
            return ActionResult.fail("获取交付物计划列表失败");
        }
    }

    /**
     * 根据项目模板ID获取交付物计划列表
     */
    @GetMapping("/getListByTemplateId")
    @Operation(summary = "根据项目模板ID获取交付物计划列表")
    public ActionResult<List<ProjectSchemaWorkproductVO>> getListByTemplateId(
        @Parameter(description = "项目模板ID") @RequestParam String projectTemplateId) {
        try {
            List<ProjectSchemaWorkproductEntity> list = projectSchemaWorkproductService.getListByTemplateId(projectTemplateId);
            List<ProjectSchemaWorkproductVO> listVO = BeanCopierUtils.copyList(list, ProjectSchemaWorkproductVO.class);
            return ActionResult.success(listVO);
        } catch (Exception e) {
            log.error("根据项目模板ID获取交付物计划列表失败", e);
            return ActionResult.fail("获取交付物计划列表失败");
        }
    }

    /**
     * 根据项目模板阶段ID获取交付物计划列表
     */
    @GetMapping("/getListByPhaseId")
    @Operation(summary = "根据项目模板阶段ID获取交付物计划列表")
    public ActionResult<List<ProjectSchemaWorkproductVO>> getListByPhaseId(
        @Parameter(description = "项目模板阶段ID") @RequestParam String schemaPhaseId) {
        try {
            List<ProjectSchemaWorkproductEntity> list = projectSchemaWorkproductService.getListByPhaseId(schemaPhaseId);
            List<ProjectSchemaWorkproductVO> listVO = BeanCopierUtils.copyList(list, ProjectSchemaWorkproductVO.class);
            return ActionResult.success(listVO);
        } catch (Exception e) {
            log.error("根据项目模板阶段ID获取交付物计划列表失败", e);
            return ActionResult.fail("获取交付物计划列表失败");
        }
    }

    /**
     * 根据项目模板WBS节点ID获取交付物计划列表
     */
    @GetMapping("/getListByWbsId")
    @Operation(summary = "根据项目模板WBS节点ID获取交付物计划列表")
    public ActionResult<List<ProjectSchemaWorkproductVO>> getListByWbsId(
        @Parameter(description = "项目模板WBS节点ID") @RequestParam String schemaWbsId) {
        try {
            List<ProjectSchemaWorkproductEntity> list = projectSchemaWorkproductService.getListByWbsId(schemaWbsId);
            List<ProjectSchemaWorkproductVO> listVO = BeanCopierUtils.copyList(list, ProjectSchemaWorkproductVO.class);
            return ActionResult.success(listVO);
        } catch (Exception e) {
            log.error("根据项目模板WBS节点ID获取交付物计划列表失败", e);
            return ActionResult.fail("获取交付物计划列表失败");
        }
    }

    /**
     * 获取交付物计划详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取交付物计划详情")
    public ActionResult<ProjectSchemaWorkproductVO> getInfo(
        @Parameter(description = "交付物计划ID") @PathVariable String id) {
        try {
            ProjectSchemaWorkproductEntity entity = projectSchemaWorkproductService.getInfo(id);
            if (entity == null) {
                return ActionResult.fail("交付物计划不存在");
            }
            
            ProjectSchemaWorkproductVO vo = BeanCopierUtils.copy(entity, ProjectSchemaWorkproductVO.class);
            return ActionResult.success(vo);
        } catch (Exception e) {
            log.error("获取交付物计划详情失败", e);
            return ActionResult.fail("获取交付物计划详情失败");
        }
    }

    /**
     * 创建交付物计划
     */
    @PostMapping
    @Operation(summary = "创建交付物计划")
    public ActionResult<String> create(@Valid @RequestBody ProjectSchemaWorkproductForm form) {
        try {
            // 检查交付物名称是否已存在
            if (projectSchemaWorkproductService.isExistByName(form.getProjectTemplateId(), form.getName(), null)) {
                return ActionResult.fail("交付物名称已存在");
            }

            ProjectSchemaWorkproductEntity entity = BeanCopierUtils.copy(form, ProjectSchemaWorkproductEntity.class);
            String id = projectSchemaWorkproductService.create(entity);
            return ActionResult.success(id);
        } catch (Exception e) {
            log.error("创建交付物计划失败", e);
            return ActionResult.fail("创建交付物计划失败");
        }
    }

    /**
     * 更新交付物计划
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新交付物计划")
    public ActionResult<Void> update(
        @Parameter(description = "交付物计划ID") @PathVariable String id,
        @Valid @RequestBody ProjectSchemaWorkproductForm form) {
        try {
            // 检查交付物计划是否存在
            ProjectSchemaWorkproductEntity existing = projectSchemaWorkproductService.getInfo(id);
            if (existing == null) {
                return ActionResult.fail("交付物计划不存在");
            }

            // 检查交付物名称是否已存在（排除当前记录）
            if (projectSchemaWorkproductService.isExistByName(form.getProjectTemplateId(), form.getName(), id)) {
                return ActionResult.fail("交付物名称已存在");
            }

            ProjectSchemaWorkproductEntity entity = BeanCopierUtils.copy(form, ProjectSchemaWorkproductEntity.class);
            projectSchemaWorkproductService.update(id, entity);
            return ActionResult.success();
        } catch (Exception e) {
            log.error("更新交付物计划失败", e);
            return ActionResult.fail("更新交付物计划失败");
        }
    }

    /**
     * 删除交付物计划
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除交付物计划")
    public ActionResult<Void> delete(@Parameter(description = "交付物计划ID") @PathVariable String id) {
        try {
            ProjectSchemaWorkproductEntity existing = projectSchemaWorkproductService.getInfo(id);
            if (existing == null) {
                return ActionResult.fail("交付物计划不存在");
            }

            projectSchemaWorkproductService.delete(id);
            return ActionResult.success();
        } catch (Exception e) {
            log.error("删除交付物计划失败", e);
            return ActionResult.fail("删除交付物计划失败");
        }
    }

    /**
     * 批量删除交付物计划
     */
    @DeleteMapping
    @Operation(summary = "批量删除交付物计划")
    public ActionResult<Void> batchDelete(@RequestBody List<String> ids) {
        try {
            projectSchemaWorkproductService.batchDelete(ids);
            return ActionResult.success();
        } catch (Exception e) {
            log.error("批量删除交付物计划失败", e);
            return ActionResult.fail("批量删除交付物计划失败");
        }
    }

    /**
     * 获取交付物计划选择列表
     */
    @GetMapping("/getSelectList")
    @Operation(summary = "获取交付物计划选择列表")
    public ActionResult<List<ProjectSchemaWorkproductVO>> getSelectList(
        @Parameter(description = "项目模板ID") @RequestParam String projectTemplateId,
        @Parameter(description = "关键字") @RequestParam(required = false) String keyword) {
        try {
            List<ProjectSchemaWorkproductEntity> list = projectSchemaWorkproductService.getSelectList(projectTemplateId, keyword);
            List<ProjectSchemaWorkproductVO> listVO = BeanCopierUtils.copyList(list, ProjectSchemaWorkproductVO.class);
            return ActionResult.success(listVO);
        } catch (Exception e) {
            log.error("获取交付物计划选择列表失败", e);
            return ActionResult.fail("获取交付物计划选择列表失败");
        }
    }

    /**
     * 从标准交付物库复制到项目模板
     */
    @PostMapping("/copyFromLibrary")
    @Operation(summary = "从标准交付物库复制到项目模板")
    public ActionResult<Integer> copyFromLibrary(
        @Parameter(description = "项目模板ID") @RequestParam String projectTemplateId,
        @Parameter(description = "标准交付物库ID列表") @RequestBody List<String> libraryWorkproductIds) {
        try {
            int count = projectSchemaWorkproductService.copyFromLibrary(projectTemplateId, libraryWorkproductIds);
            return ActionResult.success(count);
        } catch (Exception e) {
            log.error("从标准交付物库复制到项目模板失败", e);
            return ActionResult.fail("从标准交付物库复制到项目模板失败");
        }
    }

    /**
     * 复制交付物配置
     */
    @PostMapping("/copyWorkproductConfigs")
    @Operation(summary = "复制交付物配置")
    public ActionResult<Integer> copyWorkproductConfigs(
        @Parameter(description = "源模板ID") @RequestParam String sourceTemplateId,
        @Parameter(description = "目标模板ID") @RequestParam String targetTemplateId) {
        try {
            int count = projectSchemaWorkproductService.copyWorkproductConfigs(sourceTemplateId, targetTemplateId);
            return ActionResult.success(count);
        } catch (Exception e) {
            log.error("复制交付物配置失败", e);
            return ActionResult.fail("复制交付物配置失败");
        }
    }

    /**
     * 更新是否必需状态
     */
    @PutMapping("/{id}/isRequired")
    @Operation(summary = "更新是否必需状态")
    public ActionResult<Void> updateIsRequired(
        @Parameter(description = "交付物计划ID") @PathVariable String id,
        @Parameter(description = "是否必需") @RequestParam Integer isRequired) {
        try {
            projectSchemaWorkproductService.updateIsRequired(id, isRequired);
            return ActionResult.success();
        } catch (Exception e) {
            log.error("更新是否必需状态失败", e);
            return ActionResult.fail("更新是否必需状态失败");
        }
    }

    /**
     * 更新是否需要评审状态
     */
    @PutMapping("/{id}/reviewRequired")
    @Operation(summary = "更新是否需要评审状态")
    public ActionResult<Void> updateReviewRequired(
        @Parameter(description = "交付物计划ID") @PathVariable String id,
        @Parameter(description = "是否需要评审") @RequestParam Integer reviewRequired) {
        try {
            projectSchemaWorkproductService.updateReviewRequired(id, reviewRequired);
            return ActionResult.success();
        } catch (Exception e) {
            log.error("更新是否需要评审状态失败", e);
            return ActionResult.fail("更新是否需要评审状态失败");
        }
    }

    /**
     * 获取交付物计划统计信息
     */
    @GetMapping("/workproductStats")
    @Operation(summary = "获取交付物计划统计信息")
    public ActionResult<Map<String, Object>> getWorkproductStats(
        @Parameter(description = "项目模板ID") @RequestParam String projectTemplateId) {
        try {
            Map<String, Object> stats = projectSchemaWorkproductService.getWorkproductStats(projectTemplateId);
            return ActionResult.success(stats);
        } catch (Exception e) {
            log.error("获取交付物计划统计信息失败", e);
            return ActionResult.fail("获取交付物计划统计信息失败");
        }
    }

    /**
     * 获取必需交付物列表
     */
    @GetMapping("/getRequiredWorkproducts")
    @Operation(summary = "获取必需交付物列表")
    public ActionResult<List<ProjectSchemaWorkproductVO>> getRequiredWorkproducts(
        @Parameter(description = "项目模板ID") @RequestParam String projectTemplateId) {
        try {
            List<ProjectSchemaWorkproductEntity> list = projectSchemaWorkproductService.getRequiredWorkproducts(projectTemplateId);
            List<ProjectSchemaWorkproductVO> listVO = BeanCopierUtils.copyList(list, ProjectSchemaWorkproductVO.class);
            return ActionResult.success(listVO);
        } catch (Exception e) {
            log.error("获取必需交付物列表失败", e);
            return ActionResult.fail("获取必需交付物列表失败");
        }
    }

    /**
     * 获取需要评审的交付物列表
     */
    @GetMapping("/getReviewRequiredWorkproducts")
    @Operation(summary = "获取需要评审的交付物列表")
    public ActionResult<List<ProjectSchemaWorkproductVO>> getReviewRequiredWorkproducts(
        @Parameter(description = "项目模板ID") @RequestParam String projectTemplateId) {
        try {
            List<ProjectSchemaWorkproductEntity> list = projectSchemaWorkproductService.getReviewRequiredWorkproducts(projectTemplateId);
            List<ProjectSchemaWorkproductVO> listVO = BeanCopierUtils.copyList(list, ProjectSchemaWorkproductVO.class);
            return ActionResult.success(listVO);
        } catch (Exception e) {
            log.error("获取需要评审的交付物列表失败", e);
            return ActionResult.fail("获取需要评审的交付物列表失败");
        }
    }

    /**
     * 调整序号
     */
    @PostMapping("/{id}/adjustSeqNo")
    @Operation(summary = "调整序号")
    public ActionResult<Void> adjustSeqNo(
        @Parameter(description = "交付物计划ID") @PathVariable String id,
        @Parameter(description = "方向(up/down)") @RequestParam String direction) {
        try {
            projectSchemaWorkproductService.adjustSeqNo(id, direction);
            return ActionResult.success();
        } catch (Exception e) {
            log.error("调整序号失败", e);
            return ActionResult.fail("调整序号失败");
        }
    }
}