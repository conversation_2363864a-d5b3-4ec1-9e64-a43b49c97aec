package com.xinghuo.project.schema.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.exception.DataException;
import com.xinghuo.project.schema.dao.ProjectSchemaIssueMapper;
import com.xinghuo.project.schema.entity.ProjectSchemaIssueEntity;
import com.xinghuo.project.schema.model.ProjectSchemaIssuePagination;
import com.xinghuo.project.schema.model.vo.ProjectSchemaIssueVO;
import com.xinghuo.project.schema.service.ProjectSchemaIssueService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 项目模板问题清单服务实现类
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Slf4j
@Service
public class ProjectSchemaIssueServiceImpl extends BaseServiceImpl<ProjectSchemaIssueMapper, ProjectSchemaIssueEntity> implements ProjectSchemaIssueService {

    @Override
    public List<ProjectSchemaIssueVO> getList(ProjectSchemaIssuePagination pagination) {
        LambdaQueryWrapper<ProjectSchemaIssueEntity> queryWrapper = new LambdaQueryWrapper<>();
        
        // 项目模板ID条件
        if (StrXhUtil.isNotEmpty(pagination.getProjectTemplateId())) {
            queryWrapper.eq(ProjectSchemaIssueEntity::getProjectTemplateId, pagination.getProjectTemplateId());
        }
        
        // 标准问题库ID条件
        if (StrXhUtil.isNotEmpty(pagination.getLibraryIssueId())) {
            queryWrapper.eq(ProjectSchemaIssueEntity::getLibraryIssueId, pagination.getLibraryIssueId());
        }
        
        // 是否必须检查条件
        if (pagination.getIsRequired() != null) {
            queryWrapper.eq(ProjectSchemaIssueEntity::getIsRequired, pagination.getIsRequired());
        }
        
        // TODO: 关键字搜索需要根据实际的问题库表结构来实现
        // if (StrXhUtil.isNotEmpty(pagination.getKeyword())) {
        //     queryWrapper.and(wrapper -> wrapper
        //         .like(related_field_from_issue_library, pagination.getKeyword())
        //     );
        // }
        
        // 排序
        queryWrapper.orderByDesc(ProjectSchemaIssueEntity::getCreatedAt);
        
        List<ProjectSchemaIssueEntity> list = this.list(queryWrapper);
        return BeanCopierUtils.copyList(list, ProjectSchemaIssueVO.class);
    }

    @Override
    public List<ProjectSchemaIssueEntity> getListByProjectTemplateId(String projectTemplateId) {
        LambdaQueryWrapper<ProjectSchemaIssueEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectSchemaIssueEntity::getProjectTemplateId, projectTemplateId)
                   .orderByDesc(ProjectSchemaIssueEntity::getCreatedAt);
        return this.list(queryWrapper);
    }

    @Override
    public List<ProjectSchemaIssueEntity> getRequiredListByProjectTemplateId(String projectTemplateId) {
        LambdaQueryWrapper<ProjectSchemaIssueEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectSchemaIssueEntity::getProjectTemplateId, projectTemplateId)
                   .eq(ProjectSchemaIssueEntity::getIsRequired, 1)
                   .orderByDesc(ProjectSchemaIssueEntity::getCreatedAt);
        return this.list(queryWrapper);
    }

    @Override
    public ProjectSchemaIssueVO getDetailInfo(String id) {
        ProjectSchemaIssueEntity entity = this.getById(id);
        if (entity == null) {
            throw new DataException("问题清单不存在");
        }
        return BeanCopierUtils.copy(entity, ProjectSchemaIssueVO.class);
    }

    @Override
    public ProjectSchemaIssueEntity getInfo(String id) {
        ProjectSchemaIssueEntity entity = this.getById(id);
        if (entity == null) {
            throw new DataException("问题清单不存在");
        }
        return entity;
    }

    @Override
    @Transactional
    public String create(ProjectSchemaIssueVO issueVO) {
        // 检查问题是否已存在
        if (isExistByLibraryIssueId(issueVO.getProjectTemplateId(), issueVO.getLibraryIssueId(), null)) {
            throw new DataException("该项目模板中已存在此问题项");
        }
        
        ProjectSchemaIssueEntity entity = BeanCopierUtils.copy(issueVO, ProjectSchemaIssueEntity.class);
        
        // 设置默认值
        if (entity.getIsRequired() == null) {
            entity.setIsRequired(0);
        }
        
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional
    public void update(String id, ProjectSchemaIssueVO issueVO) {
        ProjectSchemaIssueEntity entity = this.getById(id);
        if (entity == null) {
            throw new DataException("问题清单不存在");
        }
        
        // 检查问题是否已存在
        if (isExistByLibraryIssueId(issueVO.getProjectTemplateId(), issueVO.getLibraryIssueId(), id)) {
            throw new DataException("该项目模板中已存在此问题项");
        }
        
        entity.setProjectTemplateId(issueVO.getProjectTemplateId());
        entity.setLibraryIssueId(issueVO.getLibraryIssueId());
        entity.setIsRequired(issueVO.getIsRequired());
        this.updateById(entity);
    }

    @Override
    @Transactional
    public void delete(String id) {
        ProjectSchemaIssueEntity entity = this.getById(id);
        if (entity == null) {
            throw new DataException("问题清单不存在");
        }
        this.removeById(id);
    }

    @Override
    @Transactional
    public void batchDelete(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        this.removeByIds(ids);
    }

    @Override
    @Transactional
    public void updateRequired(String id, Integer isRequired) {
        UpdateWrapper<ProjectSchemaIssueEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("f_id", id)
                    .set("is_required", isRequired);
        this.update(updateWrapper);
    }

    @Override
    @Transactional
    public void batchUpdateRequired(List<String> ids, Integer isRequired) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        UpdateWrapper<ProjectSchemaIssueEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("f_id", ids)
                    .set("is_required", isRequired);
        this.update(updateWrapper);
    }

    @Override
    public boolean isExistByLibraryIssueId(String projectTemplateId, String libraryIssueId, String excludeId) {
        LambdaQueryWrapper<ProjectSchemaIssueEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectSchemaIssueEntity::getProjectTemplateId, projectTemplateId)
                   .eq(ProjectSchemaIssueEntity::getLibraryIssueId, libraryIssueId);
        if (StrXhUtil.isNotEmpty(excludeId)) {
            queryWrapper.ne(ProjectSchemaIssueEntity::getId, excludeId);
        }
        return this.count(queryWrapper) > 0;
    }

    @Override
    @Transactional
    public void importFromLibrary(String projectTemplateId, List<String> libraryIssueIds) {
        if (libraryIssueIds == null || libraryIssueIds.isEmpty()) {
            return;
        }
        
        List<ProjectSchemaIssueEntity> entities = new ArrayList<>();
        for (String libraryIssueId : libraryIssueIds) {
            // 检查是否已存在
            if (!isExistByLibraryIssueId(projectTemplateId, libraryIssueId, null)) {
                ProjectSchemaIssueEntity entity = new ProjectSchemaIssueEntity();
                entity.setProjectTemplateId(projectTemplateId);
                entity.setLibraryIssueId(libraryIssueId);
                entity.setIsRequired(0); // 默认为非必须检查
                entities.add(entity);
            }
        }
        
        if (!entities.isEmpty()) {
            this.saveBatch(entities);
        }
    }

    @Override
    @Transactional
    public void copyIssues(String sourceProjectTemplateId, String targetProjectTemplateId) {
        List<ProjectSchemaIssueEntity> sourceList = getListByProjectTemplateId(sourceProjectTemplateId);
        if (sourceList.isEmpty()) {
            return;
        }
        
        List<ProjectSchemaIssueEntity> targetList = new ArrayList<>();
        for (ProjectSchemaIssueEntity source : sourceList) {
            // 检查目标模板是否已存在此问题
            if (!isExistByLibraryIssueId(targetProjectTemplateId, source.getLibraryIssueId(), null)) {
                ProjectSchemaIssueEntity target = BeanCopierUtils.copy(source, ProjectSchemaIssueEntity.class);
                target.setId(null);
                target.setProjectTemplateId(targetProjectTemplateId);
                targetList.add(target);
            }
        }
        
        if (!targetList.isEmpty()) {
            this.saveBatch(targetList);
        }
    }

    @Override
    @Transactional
    public void deleteByProjectTemplateId(String projectTemplateId) {
        LambdaQueryWrapper<ProjectSchemaIssueEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectSchemaIssueEntity::getProjectTemplateId, projectTemplateId);
        this.remove(queryWrapper);
    }

    @Override
    @Transactional
    public void deleteByLibraryIssueId(String libraryIssueId) {
        LambdaQueryWrapper<ProjectSchemaIssueEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectSchemaIssueEntity::getLibraryIssueId, libraryIssueId);
        this.remove(queryWrapper);
    }

    @Override
    @Transactional
    public void batchAddIssues(String projectTemplateId, List<String> libraryIssueIds, Integer isRequired) {
        if (libraryIssueIds == null || libraryIssueIds.isEmpty()) {
            return;
        }
        
        List<ProjectSchemaIssueEntity> entities = new ArrayList<>();
        for (String libraryIssueId : libraryIssueIds) {
            // 检查是否已存在
            if (!isExistByLibraryIssueId(projectTemplateId, libraryIssueId, null)) {
                ProjectSchemaIssueEntity entity = new ProjectSchemaIssueEntity();
                entity.setProjectTemplateId(projectTemplateId);
                entity.setLibraryIssueId(libraryIssueId);
                entity.setIsRequired(isRequired != null ? isRequired : 0);
                entities.add(entity);
            }
        }
        
        if (!entities.isEmpty()) {
            this.saveBatch(entities);
        }
    }

    @Override
    @Transactional
    public void syncLibraryIssueChanges(String libraryIssueId) {
        // TODO: 实现标准问题库变更同步逻辑
        // 这里需要根据实际的问题库表结构来实现
        log.info("同步标准问题库变更: {}", libraryIssueId);
    }

    @Override
    public List<ProjectSchemaIssueEntity> getListByCategory(String projectTemplateId, String issueCategory) {
        // TODO: 实现根据问题类别查询，需要关联问题库表
        // 这里需要根据实际的问题库表结构来实现
        log.info("根据问题类别获取问题清单: projectTemplateId={}, issueCategory={}", projectTemplateId, issueCategory);
        return new ArrayList<>();
    }

    @Override
    public List<ProjectSchemaIssueEntity> getListByLevel(String projectTemplateId, String issueLevel) {
        // TODO: 实现根据问题级别查询，需要关联问题库表
        // 这里需要根据实际的问题库表结构来实现
        log.info("根据问题级别获取问题清单: projectTemplateId={}, issueLevel={}", projectTemplateId, issueLevel);
        return new ArrayList<>();
    }
}