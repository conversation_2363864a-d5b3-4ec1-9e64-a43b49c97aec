package com.xinghuo.project.schema.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.schema.entity.ProjectSchemaWorkproductEntity;
import com.xinghuo.project.schema.model.ProjectSchemaWorkproductPagination;
import com.xinghuo.project.schema.model.vo.ProjectSchemaWorkproductVO;

import java.util.List;

/**
 * 项目模板交付物计划服务接口
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface ProjectSchemaWorkproductService extends BaseService<ProjectSchemaWorkproductEntity> {

    /**
     * 获取项目模板交付物计划列表
     *
     * @param pagination 分页参数
     * @return 交付物计划列表
     */
    List<ProjectSchemaWorkproductVO> getList(ProjectSchemaWorkproductPagination pagination);

    /**
     * 根据项目模板ID获取交付物计划列表
     *
     * @param projectTemplateId 项目模板ID
     * @return 交付物计划列表
     */
    List<ProjectSchemaWorkproductEntity> getListByProjectTemplateId(String projectTemplateId);

    /**
     * 根据阶段ID获取交付物计划列表
     *
     * @param schemaPhaseId 阶段ID
     * @return 交付物计划列表
     */
    List<ProjectSchemaWorkproductEntity> getListBySchemaPhaseId(String schemaPhaseId);

    /**
     * 根据WBS节点ID获取交付物计划列表
     *
     * @param schemaWbsId WBS节点ID
     * @return 交付物计划列表
     */
    List<ProjectSchemaWorkproductEntity> getListBySchemaWbsId(String schemaWbsId);

    /**
     * 获取交付物计划详情
     *
     * @param id 交付物计划ID
     * @return 交付物计划详情
     */
    ProjectSchemaWorkproductVO getDetailInfo(String id);

    /**
     * 获取交付物计划基本信息
     *
     * @param id 交付物计划ID
     * @return 交付物计划信息
     */
    ProjectSchemaWorkproductEntity getInfo(String id);

    /**
     * 创建项目模板交付物计划
     *
     * @param workproductVO 交付物计划信息
     * @return 交付物计划ID
     */
    String create(ProjectSchemaWorkproductVO workproductVO);

    /**
     * 更新项目模板交付物计划
     *
     * @param id 交付物计划ID
     * @param workproductVO 交付物计划信息
     */
    void update(String id, ProjectSchemaWorkproductVO workproductVO);

    /**
     * 删除项目模板交付物计划
     *
     * @param id 交付物计划ID
     */
    void delete(String id);

    /**
     * 批量删除项目模板交付物计划
     *
     * @param ids 交付物计划ID列表
     */
    void batchDelete(List<String> ids);

    /**
     * 更新必需状态
     *
     * @param id 交付物计划ID
     * @param isRequired 是否必需
     */
    void updateRequired(String id, Integer isRequired);

    /**
     * 批量更新必需状态
     *
     * @param ids 交付物计划ID列表
     * @param isRequired 是否必需
     */
    void batchUpdateRequired(List<String> ids, Integer isRequired);

    /**
     * 更新评审要求
     *
     * @param id 交付物计划ID
     * @param reviewRequired 是否需要评审
     */
    void updateReviewRequired(String id, Integer reviewRequired);

    /**
     * 检查名称是否存在
     *
     * @param projectTemplateId 项目模板ID
     * @param name 交付物名称
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByName(String projectTemplateId, String name, String excludeId);

    /**
     * 从标准交付物库导入
     *
     * @param projectTemplateId 项目模板ID
     * @param libraryWorkproductIds 标准交付物库ID列表
     */
    void importFromLibrary(String projectTemplateId, List<String> libraryWorkproductIds);

    /**
     * 复制交付物计划
     *
     * @param sourceProjectTemplateId 源项目模板ID
     * @param targetProjectTemplateId 目标项目模板ID
     */
    void copyWorkproducts(String sourceProjectTemplateId, String targetProjectTemplateId);

    /**
     * 调整显示顺序
     *
     * @param id 交付物计划ID
     * @param seqNo 新的顺序号
     */
    void updateSeqNo(String id, Integer seqNo);

    /**
     * 批量调整显示顺序
     *
     * @param workproductIds 交付物计划ID列表（按新顺序排列）
     */
    void batchUpdateSeqNo(List<String> workproductIds);

    /**
     * 根据项目模板ID删除所有交付物计划
     *
     * @param projectTemplateId 项目模板ID
     */
    void deleteByProjectTemplateId(String projectTemplateId);

    /**
     * 根据阶段ID删除交付物计划
     *
     * @param schemaPhaseId 阶段ID
     */
    void deleteBySchemaPhaseId(String schemaPhaseId);

    /**
     * 根据WBS节点ID删除交付物计划
     *
     * @param schemaWbsId WBS节点ID
     */
    void deleteBySchemaWbsId(String schemaWbsId);
}