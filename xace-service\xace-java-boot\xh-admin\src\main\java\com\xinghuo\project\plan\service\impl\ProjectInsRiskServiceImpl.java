package com.xinghuo.project.plan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.project.plan.dao.ProjectInsRiskMapper;
import com.xinghuo.project.plan.entity.ProjectInsRiskEntity;
import com.xinghuo.project.plan.model.risk.ProjectInsRiskForm;
import com.xinghuo.project.plan.model.risk.ProjectInsRiskPagination;
import com.xinghuo.project.plan.model.risk.ProjectInsRiskVO;
import com.xinghuo.project.plan.service.ProjectInsRiskService;
import java.util.List;

/**
 * 项目实际风险实例服务实现类
 */
@Service
@Transactional
public class ProjectInsRiskServiceImpl extends ServiceImpl<ProjectInsRiskMapper, ProjectInsRiskEntity> implements ProjectInsRiskService {
    
    @Override
    public ProjectInsRiskVO getInfo(String id) {
        ProjectInsRiskEntity entity = this.getById(id);
        if (entity == null) {
            return null;
        }
        
        ProjectInsRiskVO vo = BeanCopierUtils.copy(entity, ProjectInsRiskVO.class);
        // TODO: 设置关联对象的名称字段，如项目名称、分类名称、状态名称等
        return vo;
    }
    
    @Override
    public PageListVO<ProjectInsRiskVO> getList(ProjectInsRiskPagination pagination) {
        LambdaQueryWrapper<ProjectInsRiskEntity> lambda = new LambdaQueryWrapper<>();
        
        // 查询条件
        if (StrXhUtil.isNotEmpty(pagination.getProjectId())) {
            lambda.eq(ProjectInsRiskEntity::getProjectId, pagination.getProjectId());
        }
        if (StrXhUtil.isNotEmpty(pagination.getTitle())) {
            lambda.like(ProjectInsRiskEntity::getTitle, pagination.getTitle());
        }
        if (StrXhUtil.isNotEmpty(pagination.getRiskCategoryId())) {
            lambda.eq(ProjectInsRiskEntity::getRiskCategoryId, pagination.getRiskCategoryId());
        }
        if (StrXhUtil.isNotEmpty(pagination.getProbabilityLevelId())) {
            lambda.eq(ProjectInsRiskEntity::getProbabilityLevelId, pagination.getProbabilityLevelId());
        }
        if (StrXhUtil.isNotEmpty(pagination.getImpactLevelId())) {
            lambda.eq(ProjectInsRiskEntity::getImpactLevelId, pagination.getImpactLevelId());
        }
        if (StrXhUtil.isNotEmpty(pagination.getResponseUserId())) {
            lambda.eq(ProjectInsRiskEntity::getResponseUserId, pagination.getResponseUserId());
        }
        if (StrXhUtil.isNotEmpty(pagination.getStatusId())) {
            lambda.eq(ProjectInsRiskEntity::getStatusId, pagination.getStatusId());
        }
        if (pagination.getMinRiskScore() != null) {
            lambda.ge(ProjectInsRiskEntity::getRiskScore, pagination.getMinRiskScore());
        }
        if (pagination.getMaxRiskScore() != null) {
            lambda.le(ProjectInsRiskEntity::getRiskScore, pagination.getMaxRiskScore());
        }
        if (pagination.getStartTime() != null) {
            lambda.ge(ProjectInsRiskEntity::getCreatedAt, pagination.getStartTime());
        }
        if (pagination.getEndTime() != null) {
            lambda.le(ProjectInsRiskEntity::getCreatedAt, pagination.getEndTime());
        }
        if (pagination.getDueDateStart() != null) {
            lambda.ge(ProjectInsRiskEntity::getDueDate, pagination.getDueDateStart());
        }
        if (pagination.getDueDateEnd() != null) {
            lambda.le(ProjectInsRiskEntity::getDueDate, pagination.getDueDateEnd());
        }
        
        // 排序：风险评分降序，创建时间降序
        lambda.orderByDesc(ProjectInsRiskEntity::getRiskScore);
        lambda.orderByDesc(ProjectInsRiskEntity::getCreatedAt);
        
        // 分页查询
        IPage<ProjectInsRiskEntity> page = new Page<>(pagination.getCurrentPage(), pagination.getPageSize());
        IPage<ProjectInsRiskEntity> result = this.page(page, lambda);
        
        // 转换为VO
        List<ProjectInsRiskVO> list = BeanCopierUtils.copyList(result.getRecords(), ProjectInsRiskVO.class);
        // TODO: 设置关联对象的名称字段
        
        PaginationVO paginationVO = new PaginationVO();
        paginationVO.setCurrentPage(pagination.getCurrentPage());
        paginationVO.setPageSize(pagination.getPageSize());
        paginationVO.setTotal(result.getTotal());
        
        return new PageListVO<>(list, paginationVO);
    }
    
    @Override
    public List<ProjectInsRiskVO> getListByProjectId(String projectId) {
        LambdaQueryWrapper<ProjectInsRiskEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(ProjectInsRiskEntity::getProjectId, projectId);
        lambda.orderByDesc(ProjectInsRiskEntity::getRiskScore);
        lambda.orderByDesc(ProjectInsRiskEntity::getCreatedAt);
        
        List<ProjectInsRiskEntity> entities = this.list(lambda);
        List<ProjectInsRiskVO> list = BeanCopierUtils.copyList(entities, ProjectInsRiskVO.class);
        // TODO: 设置关联对象的名称字段
        
        return list;
    }
    
    @Override
    public String create(ProjectInsRiskForm form) {
        ProjectInsRiskEntity entity = BeanCopierUtils.copy(form, ProjectInsRiskEntity.class);
        entity.setId(RandomUtil.snowId());
        
        // 计算风险评分
        if (entity.getRiskScore() == null && StrXhUtil.isNotEmpty(entity.getProbabilityLevelId()) && StrXhUtil.isNotEmpty(entity.getImpactLevelId())) {
            Integer riskScore = calculateRiskScore(entity.getProbabilityLevelId(), entity.getImpactLevelId());
            entity.setRiskScore(riskScore);
        }
        
        this.save(entity);
        return entity.getId();
    }
    
    @Override
    public boolean update(ProjectInsRiskForm form) {
        ProjectInsRiskEntity entity = BeanCopierUtils.copy(form, ProjectInsRiskEntity.class);
        
        // 重新计算风险评分
        if (entity.getRiskScore() == null && StrXhUtil.isNotEmpty(entity.getProbabilityLevelId()) && StrXhUtil.isNotEmpty(entity.getImpactLevelId())) {
            Integer riskScore = calculateRiskScore(entity.getProbabilityLevelId(), entity.getImpactLevelId());
            entity.setRiskScore(riskScore);
        }
        
        return this.updateById(entity);
    }
    
    @Override
    public boolean delete(String id) {
        return this.removeById(id);
    }
    
    @Override
    public boolean deleteBatch(List<String> ids) {
        return this.removeByIds(ids);
    }
    
    @Override
    public String copyFromTemplate(String projectId, String sourceLibraryRiskId) {
        // TODO: 实现从模板库复制风险逻辑
        // 1. 从风险库获取模板风险
        // 2. 复制到项目实例
        // 3. 返回新创建的实例ID
        return null;
    }
    
    @Override
    public Integer calculateRiskScore(String probabilityLevelId, String impactLevelId) {
        // TODO: 实现风险评分计算逻辑
        // 根据概率等级和影响等级计算风险评分
        // 可能的计算公式：概率等级 * 影响等级
        // 或者从字典表获取对应的评分值
        return null;
    }
}