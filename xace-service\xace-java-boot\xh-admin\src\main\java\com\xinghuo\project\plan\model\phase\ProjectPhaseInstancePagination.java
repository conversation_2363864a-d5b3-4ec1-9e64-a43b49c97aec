package com.xinghuo.project.plan.model.phase;

import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 项目实际阶段实例分页查询参数
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "项目实际阶段实例分页查询参数")
public class ProjectPhaseInstancePagination extends Pagination {

    /**
     * 项目ID
     */
    @Schema(description = "项目ID")
    private String projectId;

    /**
     * 源项目模板阶段配置ID
     */
    @Schema(description = "源项目模板阶段配置ID")
    private String sourceSchemaPhaseId;

    /**
     * 阶段状态ID
     */
    @Schema(description = "阶段状态ID")
    private String statusId;

    /**
     * 序号最小值
     */
    @Schema(description = "序号最小值")
    private Integer seqNoMin;

    /**
     * 序号最大值
     */
    @Schema(description = "序号最大值")
    private Integer seqNoMax;

    /**
     * 计划开始日期开始
     */
    @Schema(description = "计划开始日期开始")
    private Date planStartDateStart;

    /**
     * 计划开始日期结束
     */
    @Schema(description = "计划开始日期结束")
    private Date planStartDateEnd;

    /**
     * 计划结束日期开始
     */
    @Schema(description = "计划结束日期开始")
    private Date planEndDateStart;

    /**
     * 计划结束日期结束
     */
    @Schema(description = "计划结束日期结束")
    private Date planEndDateEnd;

    /**
     * 实际开始日期开始
     */
    @Schema(description = "实际开始日期开始")
    private Date actualStartDateStart;

    /**
     * 实际开始日期结束
     */
    @Schema(description = "实际开始日期结束")
    private Date actualStartDateEnd;

    /**
     * 实际结束日期开始
     */
    @Schema(description = "实际结束日期开始")
    private Date actualEndDateStart;

    /**
     * 实际结束日期结束
     */
    @Schema(description = "实际结束日期结束")
    private Date actualEndDateEnd;

    /**
     * 审批状态ID
     */
    @Schema(description = "审批状态ID")
    private String approvalStatusId;

    /**
     * 创建时间开始
     */
    @Schema(description = "创建时间开始")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    @Schema(description = "创建时间结束")
    private LocalDateTime createTimeEnd;

    /**
     * 更新时间开始
     */
    @Schema(description = "更新时间开始")
    private LocalDateTime updateTimeStart;

    /**
     * 更新时间结束
     */
    @Schema(description = "更新时间结束")
    private LocalDateTime updateTimeEnd;

    /**
     * 关键字搜索（阶段名称、描述等）
     */
    @Schema(description = "关键字搜索")
    private String keyword;
}