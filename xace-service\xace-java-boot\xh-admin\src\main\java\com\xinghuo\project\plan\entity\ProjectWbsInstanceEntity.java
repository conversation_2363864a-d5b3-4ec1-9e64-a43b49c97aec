package com.xinghuo.project.plan.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目实际WBS计划实体类
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_ins_wbs")
public class ProjectWbsInstanceEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 所属的实际项目ID (关联 zz_project)
     */
    @TableField("PROJECT_ID")
    private String projectId;

    /**
     * 源自哪个项目模板WBS明细ID (用于追溯)
     */
    @TableField("SOURCE_SCHEMA_WBS_ID")
    private String sourceSchemaWbsId;

    /**
     * 父级节点ID (指向本表的f_id)
     */
    @TableField("PARENT_ID")
    private String parentId;

    /**
     * WBS编码
     */
    @TableField("WBS_CODE")
    private String wbsCode;

    /**
     * 活动/工作包名称
     */
    @TableField("NAME")
    private String name;

    /**
     * 显示顺序
     */
    @TableField("SEQ_NO")
    private Integer seqNo;

    /**
     * 层级深度
     */
    @TableField("LEVEL")
    private Integer level;

    /**
     * 节点类型 (1:活动/任务, 3:工作包/摘要)
     */
    @TableField("NODE_TYPE")
    private Integer nodeType;

    /**
     * 是否是里程碑
     */
    @TableField("IS_MILESTONE")
    private Integer isMilestone;

    /**
     * 计划开始日期
     */
    @TableField("PLAN_START_DATE")
    private Date planStartDate;

    /**
     * 计划结束日期
     */
    @TableField("PLAN_END_DATE")
    private Date planEndDate;

    /**
     * 计划工期 (天)
     */
    @TableField("PLAN_DURATION")
    private BigDecimal planDuration;

    /**
     * 计划工时 (小时)
     */
    @TableField("PLAN_HOUR")
    private BigDecimal planHour;

    /**
     * 约束类型ID
     */
    @TableField("CONSTRAINT_TYPE_ID")
    private String constraintTypeId;

    /**
     * 约束日期
     */
    @TableField("CONSTRAINT_DATE")
    private Date constraintDate;

    /**
     * 实际开始日期
     */
    @TableField("ACTUAL_START_DATE")
    private Date actualStartDate;

    /**
     * 实际结束日期
     */
    @TableField("ACTUAL_END_DATE")
    private Date actualEndDate;

    /**
     * 实际发生工时 (由工时表汇总)
     */
    @TableField("ACTUAL_HOUR")
    private BigDecimal actualHour;

    /**
     * 完成百分比 (0-100)
     */
    @TableField("PROGRESS")
    private Integer progress;

    /**
     * 任务状态ID (关联字典表)
     */
    @TableField("STATUS_ID")
    private String statusId;

    /**
     * 责任人ID (具体的用户ID)
     */
    @TableField("RESPONSE_USER_ID")
    private String responseUserId;

    /**
     * 完成方式ID
     */
    @TableField("COMPLETE_TYPE_ID")
    private String completeTypeId;

    /**
     * 确认人ID
     */
    @TableField("CONFIRM_USER_ID")
    private String confirmUserId;

    /**
     * 前置任务ID列表 (指向本表的f_id)
     */
    @TableField("PREDECESSORS")
    private String predecessors;
}