package com.xinghuo.project.plan.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import java.util.Date;

/**
 * 项目实际问题实例实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_ins_issue")
public class ProjectInsIssueEntity extends BaseEntityV2.CUBaseEntityV2<String> {
    
    /**
     * 所属的实际项目ID
     */
    @TableField("PROJECT_ID")
    private String projectId;
    
    /**
     * 源自哪个标准问题库ID
     */
    @TableField("SOURCE_LIBRARY_ISSUE_ID")
    private String sourceLibraryIssueId;
    
    /**
     * 问题标题
     */
    @TableField("TITLE")
    private String title;
    
    /**
     * 问题描述
     */
    @TableField("DESCRIPTION")
    private String description;
    
    /**
     * 问题分类ID
     */
    @TableField("ISSUE_CATEGORY_ID")
    private String issueCategoryId;
    
    /**
     * 优先级ID
     */
    @TableField("PRIORITY_ID")
    private String priorityId;
    
    /**
     * 解决者用户ID
     */
    @TableField("SOLVER_USER_ID")
    private String solverUserId;
    
    /**
     * 截止日期
     */
    @TableField("DUE_DATE")
    private Date dueDate;
    
    /**
     * 解决方案
     */
    @TableField("SOLUTION")
    private String solution;
    
    /**
     * 状态ID
     */
    @TableField("STATUS_ID")
    private String statusId;
    
    /**
     * 日志记录
     */
    @TableField("LOG")
    private String log;
    
    /**
     * 关联任务ID
     */
    @TableField("RELATED_TASK_ID")
    private String relatedTaskId;
}