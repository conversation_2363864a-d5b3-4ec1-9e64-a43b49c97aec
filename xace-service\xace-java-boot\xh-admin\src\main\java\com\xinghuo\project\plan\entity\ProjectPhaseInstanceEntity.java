package com.xinghuo.project.plan.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import java.util.Date;

/**
 * 项目实际阶段实例实体类
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_ins_phase")
public class ProjectPhaseInstanceEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 所属的实际项目ID (关联 zz_project)
     */
    @TableField("PROJECT_ID")
    private String projectId;

    /**
     * 源自哪个项目模板阶段配置ID (用于追溯)
     */
    @TableField("SOURCE_SCHEMA_PHASE_ID")
    private String sourceSchemaPhaseId;

    /**
     * 阶段名称
     */
    @TableField("NAME")
    private String name;

    /**
     * 阶段描述
     */
    @TableField("DESCRIPTION")
    private String description;

    /**
     * 该阶段在此项目中的顺序
     */
    @TableField("SEQ_NO")
    private Integer seqNo;

    /**
     * 阶段状态ID (关联字典表, 如: 未开始, 进行中, 已完成)
     */
    @TableField("STATUS_ID")
    private String statusId;

    /**
     * 计划开始日期
     */
    @TableField("PLAN_START_DATE")
    private Date planStartDate;

    /**
     * 计划结束日期
     */
    @TableField("PLAN_END_DATE")
    private Date planEndDate;

    /**
     * 实际开始日期
     */
    @TableField("ACTUAL_START_DATE")
    private Date actualStartDate;

    /**
     * 实际结束日期
     */
    @TableField("ACTUAL_END_DATE")
    private Date actualEndDate;

    /**
     * 阶段完成审批状态ID (关联字典表)
     */
    @TableField("APPROVAL_STATUS_ID")
    private String approvalStatusId;

    /**
     * 关联的实际审批流程实例ID
     */
    @TableField("APPROVAL_FLOW_INSTANCE_ID")
    private String approvalFlowInstanceId;

    /**
     * 关联的实际检查单实例ID
     */
    @TableField("CHECKLIST_INSTANCE_ID")
    private String checklistInstanceId;
}