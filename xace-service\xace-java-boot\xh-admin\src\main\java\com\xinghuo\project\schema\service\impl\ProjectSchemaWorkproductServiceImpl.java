package com.xinghuo.project.schema.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.exception.DataException;
import com.xinghuo.project.schema.dao.ProjectSchemaWorkproductMapper;
import com.xinghuo.project.schema.entity.ProjectSchemaWorkproductEntity;
import com.xinghuo.project.schema.model.ProjectSchemaWorkproductPagination;
import com.xinghuo.project.schema.model.vo.ProjectSchemaWorkproductVO;
import com.xinghuo.project.schema.service.ProjectSchemaWorkproductService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 项目模板交付物计划服务实现类
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Slf4j
@Service
public class ProjectSchemaWorkproductServiceImpl extends BaseServiceImpl<ProjectSchemaWorkproductMapper, ProjectSchemaWorkproductEntity> implements ProjectSchemaWorkproductService {

    @Override
    public List<ProjectSchemaWorkproductVO> getList(ProjectSchemaWorkproductPagination pagination) {
        LambdaQueryWrapper<ProjectSchemaWorkproductEntity> queryWrapper = new LambdaQueryWrapper<>();
        
        // 项目模板ID条件
        if (StrXhUtil.isNotEmpty(pagination.getProjectTemplateId())) {
            queryWrapper.eq(ProjectSchemaWorkproductEntity::getProjectTemplateId, pagination.getProjectTemplateId());
        }
        
        // 交付物名称条件
        if (StrXhUtil.isNotEmpty(pagination.getName())) {
            queryWrapper.like(ProjectSchemaWorkproductEntity::getName, pagination.getName());
        }
        
        // 交付物类型条件
        if (StrXhUtil.isNotEmpty(pagination.getTypeId())) {
            queryWrapper.eq(ProjectSchemaWorkproductEntity::getTypeId, pagination.getTypeId());
        }
        
        // 交付物子类型条件
        if (StrXhUtil.isNotEmpty(pagination.getSubTypeId())) {
            queryWrapper.eq(ProjectSchemaWorkproductEntity::getSubTypeId, pagination.getSubTypeId());
        }
        
        // 是否必需条件
        if (pagination.getIsRequired() != null) {
            queryWrapper.eq(ProjectSchemaWorkproductEntity::getIsRequired, pagination.getIsRequired());
        }
        
        // 阶段ID条件
        if (StrXhUtil.isNotEmpty(pagination.getSchemaPhaseId())) {
            queryWrapper.eq(ProjectSchemaWorkproductEntity::getSchemaPhaseId, pagination.getSchemaPhaseId());
        }
        
        // WBS节点ID条件
        if (StrXhUtil.isNotEmpty(pagination.getSchemaWbsId())) {
            queryWrapper.eq(ProjectSchemaWorkproductEntity::getSchemaWbsId, pagination.getSchemaWbsId());
        }
        
        // 责任角色ID条件
        if (StrXhUtil.isNotEmpty(pagination.getResponseRoleId())) {
            queryWrapper.eq(ProjectSchemaWorkproductEntity::getResponseRoleId, pagination.getResponseRoleId());
        }
        
        // 是否需要评审条件
        if (pagination.getReviewRequired() != null) {
            queryWrapper.eq(ProjectSchemaWorkproductEntity::getReviewRequired, pagination.getReviewRequired());
        }
        
        // 关键字搜索（名称或描述）
        if (StrXhUtil.isNotEmpty(pagination.getKeyword())) {
            queryWrapper.and(wrapper -> wrapper
                .like(ProjectSchemaWorkproductEntity::getName, pagination.getKeyword())
                .or()
                .like(ProjectSchemaWorkproductEntity::getDescription, pagination.getKeyword())
            );
        }
        
        // 排序
        queryWrapper.orderByAsc(ProjectSchemaWorkproductEntity::getSeqNo)
                   .orderByDesc(ProjectSchemaWorkproductEntity::getCreatedAt);
        
        List<ProjectSchemaWorkproductEntity> list = this.list(queryWrapper);
        return BeanCopierUtils.copyList(list, ProjectSchemaWorkproductVO.class);
    }

    @Override
    public List<ProjectSchemaWorkproductEntity> getListByProjectTemplateId(String projectTemplateId) {
        LambdaQueryWrapper<ProjectSchemaWorkproductEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectSchemaWorkproductEntity::getProjectTemplateId, projectTemplateId)
                   .orderByAsc(ProjectSchemaWorkproductEntity::getSeqNo);
        return this.list(queryWrapper);
    }

    @Override
    public List<ProjectSchemaWorkproductEntity> getListBySchemaPhaseId(String schemaPhaseId) {
        LambdaQueryWrapper<ProjectSchemaWorkproductEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectSchemaWorkproductEntity::getSchemaPhaseId, schemaPhaseId)
                   .orderByAsc(ProjectSchemaWorkproductEntity::getSeqNo);
        return this.list(queryWrapper);
    }

    @Override
    public List<ProjectSchemaWorkproductEntity> getListBySchemaWbsId(String schemaWbsId) {
        LambdaQueryWrapper<ProjectSchemaWorkproductEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectSchemaWorkproductEntity::getSchemaWbsId, schemaWbsId)
                   .orderByAsc(ProjectSchemaWorkproductEntity::getSeqNo);
        return this.list(queryWrapper);
    }

    @Override
    public ProjectSchemaWorkproductVO getDetailInfo(String id) {
        ProjectSchemaWorkproductEntity entity = this.getById(id);
        if (entity == null) {
            throw new DataException("交付物计划不存在");
        }
        return BeanCopierUtils.copy(entity, ProjectSchemaWorkproductVO.class);
    }

    @Override
    public ProjectSchemaWorkproductEntity getInfo(String id) {
        ProjectSchemaWorkproductEntity entity = this.getById(id);
        if (entity == null) {
            throw new DataException("交付物计划不存在");
        }
        return entity;
    }

    @Override
    @Transactional
    public String create(ProjectSchemaWorkproductVO workproductVO) {
        // 检查名称是否重复
        if (isExistByName(workproductVO.getProjectTemplateId(), workproductVO.getName(), null)) {
            throw new DataException("该项目模板中已存在同名的交付物计划");
        }
        
        ProjectSchemaWorkproductEntity entity = BeanCopierUtils.copy(workproductVO, ProjectSchemaWorkproductEntity.class);
        
        // 设置默认值
        if (entity.getSeqNo() == null) {
            entity.setSeqNo(getNextSeqNo(entity.getProjectTemplateId()));
        }
        if (entity.getIsRequired() == null) {
            entity.setIsRequired(1);
        }
        if (entity.getReviewRequired() == null) {
            entity.setReviewRequired(0);
        }
        
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional
    public void update(String id, ProjectSchemaWorkproductVO workproductVO) {
        ProjectSchemaWorkproductEntity entity = this.getById(id);
        if (entity == null) {
            throw new DataException("交付物计划不存在");
        }
        
        // 检查名称是否重复
        if (isExistByName(workproductVO.getProjectTemplateId(), workproductVO.getName(), id)) {
            throw new DataException("该项目模板中已存在同名的交付物计划");
        }
        
        entity.setProjectTemplateId(workproductVO.getProjectTemplateId());
        entity.setLibraryWorkproductId(workproductVO.getLibraryWorkproductId());
        entity.setName(workproductVO.getName());
        entity.setDescription(workproductVO.getDescription());
        entity.setTypeId(workproductVO.getTypeId());
        entity.setSubTypeId(workproductVO.getSubTypeId());
        entity.setSeqNo(workproductVO.getSeqNo());
        entity.setIsRequired(workproductVO.getIsRequired());
        entity.setResponseRoleId(workproductVO.getResponseRoleId());
        entity.setReviewRequired(workproductVO.getReviewRequired());
        entity.setSchemaPhaseId(workproductVO.getSchemaPhaseId());
        entity.setSchemaWbsId(workproductVO.getSchemaWbsId());
        this.updateById(entity);
    }

    @Override
    @Transactional
    public void delete(String id) {
        ProjectSchemaWorkproductEntity entity = this.getById(id);
        if (entity == null) {
            throw new DataException("交付物计划不存在");
        }
        this.removeById(id);
    }

    @Override
    @Transactional
    public void batchDelete(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        this.removeByIds(ids);
    }

    @Override
    @Transactional
    public void updateRequired(String id, Integer isRequired) {
        UpdateWrapper<ProjectSchemaWorkproductEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("f_id", id)
                    .set("is_required", isRequired);
        this.update(updateWrapper);
    }

    @Override
    @Transactional
    public void batchUpdateRequired(List<String> ids, Integer isRequired) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        UpdateWrapper<ProjectSchemaWorkproductEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("f_id", ids)
                    .set("is_required", isRequired);
        this.update(updateWrapper);
    }

    @Override
    @Transactional
    public void updateReviewRequired(String id, Integer reviewRequired) {
        UpdateWrapper<ProjectSchemaWorkproductEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("f_id", id)
                    .set("review_required", reviewRequired);
        this.update(updateWrapper);
    }

    @Override
    public boolean isExistByName(String projectTemplateId, String name, String excludeId) {
        LambdaQueryWrapper<ProjectSchemaWorkproductEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectSchemaWorkproductEntity::getProjectTemplateId, projectTemplateId)
                   .eq(ProjectSchemaWorkproductEntity::getName, name);
        if (StrXhUtil.isNotEmpty(excludeId)) {
            queryWrapper.ne(ProjectSchemaWorkproductEntity::getId, excludeId);
        }
        return this.count(queryWrapper) > 0;
    }

    @Override
    @Transactional
    public void importFromLibrary(String projectTemplateId, List<String> libraryWorkproductIds) {
        // TODO: 实现从标准交付物库导入的逻辑
        // 这里需要根据实际的标准交付物库表结构来实现
        log.info("从标准交付物库导入到项目模板: {}, 库ID列表: {}", projectTemplateId, libraryWorkproductIds);
    }

    @Override
    @Transactional
    public void copyWorkproducts(String sourceProjectTemplateId, String targetProjectTemplateId) {
        List<ProjectSchemaWorkproductEntity> sourceList = getListByProjectTemplateId(sourceProjectTemplateId);
        if (sourceList.isEmpty()) {
            return;
        }
        
        List<ProjectSchemaWorkproductEntity> targetList = new ArrayList<>();
        for (ProjectSchemaWorkproductEntity source : sourceList) {
            ProjectSchemaWorkproductEntity target = BeanCopierUtils.copy(source, ProjectSchemaWorkproductEntity.class);
            target.setId(null);
            target.setProjectTemplateId(targetProjectTemplateId);
            targetList.add(target);
        }
        
        this.saveBatch(targetList);
    }

    @Override
    @Transactional
    public void updateSeqNo(String id, Integer seqNo) {
        UpdateWrapper<ProjectSchemaWorkproductEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("f_id", id)
                    .set("seq_no", seqNo);
        this.update(updateWrapper);
    }

    @Override
    @Transactional
    public void batchUpdateSeqNo(List<String> workproductIds) {
        if (workproductIds == null || workproductIds.isEmpty()) {
            return;
        }
        
        for (int i = 0; i < workproductIds.size(); i++) {
            updateSeqNo(workproductIds.get(i), i + 1);
        }
    }

    @Override
    @Transactional
    public void deleteByProjectTemplateId(String projectTemplateId) {
        LambdaQueryWrapper<ProjectSchemaWorkproductEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectSchemaWorkproductEntity::getProjectTemplateId, projectTemplateId);
        this.remove(queryWrapper);
    }

    @Override
    @Transactional
    public void deleteBySchemaPhaseId(String schemaPhaseId) {
        LambdaQueryWrapper<ProjectSchemaWorkproductEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectSchemaWorkproductEntity::getSchemaPhaseId, schemaPhaseId);
        this.remove(queryWrapper);
    }

    @Override
    @Transactional
    public void deleteBySchemaWbsId(String schemaWbsId) {
        LambdaQueryWrapper<ProjectSchemaWorkproductEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectSchemaWorkproductEntity::getSchemaWbsId, schemaWbsId);
        this.remove(queryWrapper);
    }

    /**
     * 获取下一个排序号
     */
    private Integer getNextSeqNo(String projectTemplateId) {
        LambdaQueryWrapper<ProjectSchemaWorkproductEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectSchemaWorkproductEntity::getProjectTemplateId, projectTemplateId)
                   .orderByDesc(ProjectSchemaWorkproductEntity::getSeqNo)
                   .last("limit 1");
        
        ProjectSchemaWorkproductEntity lastEntity = this.getOne(queryWrapper);
        return lastEntity != null && lastEntity.getSeqNo() != null ? lastEntity.getSeqNo() + 1 : 1;
    }
}