package com.xinghuo.project.schema.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.schema.entity.ProjectSchemaRiskEntity;
import com.xinghuo.project.schema.model.ProjectSchemaRiskPagination;
import com.xinghuo.project.schema.model.vo.ProjectSchemaRiskVO;

import java.util.List;

/**
 * 项目模板风险清单服务接口
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface ProjectSchemaRiskService extends BaseService<ProjectSchemaRiskEntity> {

    /**
     * 获取项目模板风险清单列表
     *
     * @param pagination 分页参数
     * @return 风险清单列表
     */
    List<ProjectSchemaRiskVO> getList(ProjectSchemaRiskPagination pagination);

    /**
     * 根据项目模板ID获取风险清单列表
     *
     * @param projectTemplateId 项目模板ID
     * @return 风险清单列表
     */
    List<ProjectSchemaRiskEntity> getListByProjectTemplateId(String projectTemplateId);

    /**
     * 根据项目模板ID获取必须识别的风险清单
     *
     * @param projectTemplateId 项目模板ID
     * @return 必须识别的风险清单列表
     */
    List<ProjectSchemaRiskEntity> getRequiredListByProjectTemplateId(String projectTemplateId);

    /**
     * 获取风险清单详情
     *
     * @param id 风险清单ID
     * @return 风险清单详情
     */
    ProjectSchemaRiskVO getDetailInfo(String id);

    /**
     * 获取风险清单基本信息
     *
     * @param id 风险清单ID
     * @return 风险清单信息
     */
    ProjectSchemaRiskEntity getInfo(String id);

    /**
     * 创建项目模板风险清单
     *
     * @param riskVO 风险清单信息
     * @return 风险清单ID
     */
    String create(ProjectSchemaRiskVO riskVO);

    /**
     * 更新项目模板风险清单
     *
     * @param id 风险清单ID
     * @param riskVO 风险清单信息
     */
    void update(String id, ProjectSchemaRiskVO riskVO);

    /**
     * 删除项目模板风险清单
     *
     * @param id 风险清单ID
     */
    void delete(String id);

    /**
     * 批量删除项目模板风险清单
     *
     * @param ids 风险清单ID列表
     */
    void batchDelete(List<String> ids);

    /**
     * 更新必须识别状态
     *
     * @param id 风险清单ID
     * @param isRequired 是否必须识别
     */
    void updateRequired(String id, Integer isRequired);

    /**
     * 批量更新必须识别状态
     *
     * @param ids 风险清单ID列表
     * @param isRequired 是否必须识别
     */
    void batchUpdateRequired(List<String> ids, Integer isRequired);

    /**
     * 检查风险是否已存在
     *
     * @param projectTemplateId 项目模板ID
     * @param libraryRiskId 标准风险库ID
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByLibraryRiskId(String projectTemplateId, String libraryRiskId, String excludeId);

    /**
     * 从标准风险库导入
     *
     * @param projectTemplateId 项目模板ID
     * @param libraryRiskIds 标准风险库ID列表
     */
    void importFromLibrary(String projectTemplateId, List<String> libraryRiskIds);

    /**
     * 复制风险清单
     *
     * @param sourceProjectTemplateId 源项目模板ID
     * @param targetProjectTemplateId 目标项目模板ID
     */
    void copyRisks(String sourceProjectTemplateId, String targetProjectTemplateId);

    /**
     * 根据项目模板ID删除所有风险清单
     *
     * @param projectTemplateId 项目模板ID
     */
    void deleteByProjectTemplateId(String projectTemplateId);

    /**
     * 根据标准风险库ID删除风险清单
     *
     * @param libraryRiskId 标准风险库ID
     */
    void deleteByLibraryRiskId(String libraryRiskId);

    /**
     * 批量添加标准风险库到项目模板
     *
     * @param projectTemplateId 项目模板ID
     * @param libraryRiskIds 标准风险库ID列表
     * @param isRequired 是否必须识别
     */
    void batchAddRisks(String projectTemplateId, List<String> libraryRiskIds, Integer isRequired);

    /**
     * 同步标准风险库变更
     *
     * @param libraryRiskId 标准风险库ID
     */
    void syncLibraryRiskChanges(String libraryRiskId);
}