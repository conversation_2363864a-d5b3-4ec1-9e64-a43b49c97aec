package com.xinghuo.project.plan.model.phase;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 项目实际阶段实例视图对象
 * 用于返回阶段实例列表和详情信息
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@Schema(description = "项目实际阶段实例视图对象")
public class ProjectPhaseInstanceVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 所属的实际项目ID
     */
    @Schema(description = "所属的实际项目ID")
    private String projectId;

    /**
     * 项目名称（冗余字段，便于显示）
     */
    @Schema(description = "项目名称")
    private String projectName;

    /**
     * 源自哪个项目模板阶段配置ID
     */
    @Schema(description = "源项目模板阶段配置ID")
    private String sourceSchemaPhaseId;

    /**
     * 阶段名称
     */
    @Schema(description = "阶段名称")
    private String name;

    /**
     * 阶段描述
     */
    @Schema(description = "阶段描述")
    private String description;

    /**
     * 该阶段在此项目中的顺序
     */
    @Schema(description = "序号")
    private Integer seqNo;

    /**
     * 阶段状态ID
     */
    @Schema(description = "阶段状态ID")
    private String statusId;

    /**
     * 阶段状态名称（冗余字段，便于显示）
     */
    @Schema(description = "阶段状态名称")
    private String statusName;

    /**
     * 计划开始日期
     */
    @Schema(description = "计划开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planStartDate;

    /**
     * 计划结束日期
     */
    @Schema(description = "计划结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planEndDate;

    /**
     * 实际开始日期
     */
    @Schema(description = "实际开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date actualStartDate;

    /**
     * 实际结束日期
     */
    @Schema(description = "实际结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date actualEndDate;

    /**
     * 阶段完成审批状态ID
     */
    @Schema(description = "审批状态ID")
    private String approvalStatusId;

    /**
     * 审批状态名称（冗余字段，便于显示）
     */
    @Schema(description = "审批状态名称")
    private String approvalStatusName;

    /**
     * 关联的实际审批流程实例ID
     */
    @Schema(description = "审批流程实例ID")
    private String approvalFlowInstanceId;

    /**
     * 关联的实际检查单实例ID
     */
    @Schema(description = "检查单实例ID")
    private String checklistInstanceId;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private String tenantId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 创建用户ID
     */
    @Schema(description = "创建用户ID")
    private String createdBy;

    /**
     * 创建用户名称 (冗余字段，便于显示)
     */
    @Schema(description = "创建用户名称")
    private String createdByName;

    /**
     * 最后修改时间
     */
    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdatedAt;

    /**
     * 最后修改用户ID
     */
    @Schema(description = "最后修改用户ID")
    private String lastUpdatedBy;

    /**
     * 最后修改用户名称 (冗余字段，便于显示)
     */
    @Schema(description = "最后修改用户名称")
    private String lastUpdatedByName;
}