package com.xinghuo.project.plan.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.project.plan.model.issue.ProjectInsIssueForm;
import com.xinghuo.project.plan.model.issue.ProjectInsIssuePagination;
import com.xinghuo.project.plan.model.issue.ProjectInsIssueVO;
import com.xinghuo.project.plan.service.ProjectInsIssueService;
import java.util.List;

/**
 * 项目实际问题实例控制器
 */
@Tag(name = "项目实际问题实例管理")
@RestController
@RequestMapping("/api/project/plan/issue")
public class ProjectInsIssueController {
    
    @Autowired
    private ProjectInsIssueService projectInsIssueService;
    
    @Operation(summary = "获取问题实例详情")
    @GetMapping("/{id}")
    public ActionResult<ProjectInsIssueVO> getInfo(@Parameter(description = "问题实例ID") @PathVariable String id) {
        ProjectInsIssueVO info = projectInsIssueService.getInfo(id);
        return ActionResult.success(info);
    }
    
    @Operation(summary = "分页查询问题实例列表")
    @GetMapping
    public ActionResult<PageListVO<ProjectInsIssueVO>> getList(ProjectInsIssuePagination pagination) {
        PageListVO<ProjectInsIssueVO> result = projectInsIssueService.getList(pagination);
        return ActionResult.success(result);
    }
    
    @Operation(summary = "根据项目ID获取问题列表")
    @GetMapping("/project/{projectId}")
    public ActionResult<List<ProjectInsIssueVO>> getListByProjectId(@Parameter(description = "项目ID") @PathVariable String projectId) {
        List<ProjectInsIssueVO> list = projectInsIssueService.getListByProjectId(projectId);
        return ActionResult.success(list);
    }
    
    @Operation(summary = "创建问题实例")
    @PostMapping
    public ActionResult<String> create(@Valid @RequestBody ProjectInsIssueForm form) {
        String id = projectInsIssueService.create(form);
        return ActionResult.success(id);
    }
    
    @Operation(summary = "更新问题实例")
    @PutMapping("/{id}")
    public ActionResult<Boolean> update(@Parameter(description = "问题实例ID") @PathVariable String id, @Valid @RequestBody ProjectInsIssueForm form) {
        form.setId(id);
        boolean result = projectInsIssueService.update(form);
        return ActionResult.success(result);
    }
    
    @Operation(summary = "删除问题实例")
    @DeleteMapping("/{id}")
    public ActionResult<Boolean> delete(@Parameter(description = "问题实例ID") @PathVariable String id) {
        boolean result = projectInsIssueService.delete(id);
        return ActionResult.success(result);
    }
    
    @Operation(summary = "批量删除问题实例")
    @DeleteMapping("/batch")
    public ActionResult<Boolean> deleteBatch(@RequestBody List<String> ids) {
        boolean result = projectInsIssueService.deleteBatch(ids);
        return ActionResult.success(result);
    }
    
    @Operation(summary = "从模板库复制问题到项目")
    @PostMapping("/copy-from-template")
    public ActionResult<String> copyFromTemplate(@Parameter(description = "项目ID") @RequestParam String projectId, 
                                                 @Parameter(description = "源问题库ID") @RequestParam String sourceLibraryIssueId) {
        String id = projectInsIssueService.copyFromTemplate(projectId, sourceLibraryIssueId);
        return ActionResult.success(id);
    }
}