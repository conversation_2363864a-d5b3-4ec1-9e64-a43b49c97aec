package com.xinghuo.project.plan.model.workproduct;

import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 项目模板交付物计划分页查询参数
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "项目模板交付物计划分页查询参数")
public class ProjectSchemaWorkproductPagination extends Pagination {

    /**
     * 项目模板ID
     */
    @Schema(description = "项目模板ID")
    private String projectTemplateId;

    /**
     * 标准交付物库ID
     */
    @Schema(description = "标准交付物库ID")
    private String libraryWorkproductId;

    /**
     * 交付物类型ID
     */
    @Schema(description = "交付物类型ID")
    private String typeId;

    /**
     * 交付物子类型ID
     */
    @Schema(description = "交付物子类型ID")
    private String subTypeId;

    /**
     * 序号最小值
     */
    @Schema(description = "序号最小值")
    private Integer seqNoMin;

    /**
     * 序号最大值
     */
    @Schema(description = "序号最大值")
    private Integer seqNoMax;

    /**
     * 是否是必需交付物
     */
    @Schema(description = "是否是必需交付物（1:是, 0:否）")
    private Integer isRequired;

    /**
     * 责任角色ID
     */
    @Schema(description = "责任角色ID")
    private String responseRoleId;

    /**
     * 是否需要评审
     */
    @Schema(description = "是否需要评审（1:是, 0:否）")
    private Integer reviewRequired;

    /**
     * 关联的项目模板阶段ID
     */
    @Schema(description = "关联的项目模板阶段ID")
    private String schemaPhaseId;

    /**
     * 关联的项目模板WBS节点ID
     */
    @Schema(description = "关联的项目模板WBS节点ID")
    private String schemaWbsId;

    /**
     * 创建时间开始
     */
    @Schema(description = "创建时间开始")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    @Schema(description = "创建时间结束")
    private LocalDateTime createTimeEnd;

    /**
     * 更新时间开始
     */
    @Schema(description = "更新时间开始")
    private LocalDateTime updateTimeStart;

    /**
     * 更新时间结束
     */
    @Schema(description = "更新时间结束")
    private LocalDateTime updateTimeEnd;

    /**
     * 关键字搜索（交付物名称、描述等）
     */
    @Schema(description = "关键字搜索")
    private String keyword;
}