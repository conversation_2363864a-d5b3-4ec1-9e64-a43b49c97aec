package com.xinghuo.project.plan.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.project.plan.entity.ProjectPhaseInstanceEntity;
import com.xinghuo.project.plan.model.phase.ProjectPhaseInstanceForm;
import com.xinghuo.project.plan.model.phase.ProjectPhaseInstancePagination;
import com.xinghuo.project.plan.model.phase.ProjectPhaseInstanceVO;
import com.xinghuo.project.plan.service.ProjectPhaseInstanceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 项目实际阶段实例管理控制器
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Slf4j
@Tag(name = "项目阶段实例管理", description = "项目实际阶段实例管理相关接口")
@RestController
@RequestMapping("/api/project/plan/projectPhaseInstance")
public class ProjectPhaseInstanceController {

    @Resource
    private ProjectPhaseInstanceService projectPhaseInstanceService;

    /**
     * 获取阶段实例列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取阶段实例列表")
    public ActionResult<PageListVO<ProjectPhaseInstanceVO>> list(@RequestBody ProjectPhaseInstancePagination pagination) {
        try {
            List<ProjectPhaseInstanceEntity> list = projectPhaseInstanceService.getList(pagination);
            List<ProjectPhaseInstanceVO> listVO = BeanCopierUtils.copyList(list, ProjectPhaseInstanceVO.class);

            PageListVO<ProjectPhaseInstanceVO> vo = new PageListVO<>();
            vo.setList(listVO);
            return ActionResult.success(vo);
        } catch (Exception e) {
            log.error("获取阶段实例列表失败", e);
            return ActionResult.fail("获取阶段实例列表失败");
        }
    }

    /**
     * 根据项目ID获取阶段实例列表
     */
    @GetMapping("/getListByProjectId")
    @Operation(summary = "根据项目ID获取阶段实例列表")
    public ActionResult<List<ProjectPhaseInstanceVO>> getListByProjectId(
        @Parameter(description = "项目ID") @RequestParam String projectId) {
        try {
            List<ProjectPhaseInstanceEntity> list = projectPhaseInstanceService.getListByProjectId(projectId);
            List<ProjectPhaseInstanceVO> listVO = BeanCopierUtils.copyList(list, ProjectPhaseInstanceVO.class);
            return ActionResult.success(listVO);
        } catch (Exception e) {
            log.error("根据项目ID获取阶段实例列表失败", e);
            return ActionResult.fail("获取阶段实例列表失败");
        }
    }

    /**
     * 获取阶段实例详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取阶段实例详情")
    public ActionResult<ProjectPhaseInstanceVO> getInfo(
        @Parameter(description = "阶段实例ID") @PathVariable String id) {
        try {
            ProjectPhaseInstanceEntity entity = projectPhaseInstanceService.getInfo(id);
            if (entity == null) {
                return ActionResult.fail("阶段实例不存在");
            }
            
            ProjectPhaseInstanceVO vo = BeanCopierUtils.copy(entity, ProjectPhaseInstanceVO.class);
            return ActionResult.success(vo);
        } catch (Exception e) {
            log.error("获取阶段实例详情失败", e);
            return ActionResult.fail("获取阶段实例详情失败");
        }
    }

    /**
     * 创建阶段实例
     */
    @PostMapping
    @Operation(summary = "创建阶段实例")
    public ActionResult<String> create(@Valid @RequestBody ProjectPhaseInstanceForm form) {
        try {
            // 检查阶段名称是否已存在
            if (projectPhaseInstanceService.isExistByName(form.getProjectId(), form.getName(), null)) {
                return ActionResult.fail("阶段名称已存在");
            }

            ProjectPhaseInstanceEntity entity = BeanCopierUtils.copy(form, ProjectPhaseInstanceEntity.class);
            String id = projectPhaseInstanceService.create(entity);
            return ActionResult.success(id);
        } catch (Exception e) {
            log.error("创建阶段实例失败", e);
            return ActionResult.fail("创建阶段实例失败");
        }
    }

    /**
     * 更新阶段实例
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新阶段实例")
    public ActionResult<Void> update(
        @Parameter(description = "阶段实例ID") @PathVariable String id,
        @Valid @RequestBody ProjectPhaseInstanceForm form) {
        try {
            // 检查阶段实例是否存在
            ProjectPhaseInstanceEntity existing = projectPhaseInstanceService.getInfo(id);
            if (existing == null) {
                return ActionResult.fail("阶段实例不存在");
            }

            // 检查阶段名称是否已存在（排除当前记录）
            if (projectPhaseInstanceService.isExistByName(form.getProjectId(), form.getName(), id)) {
                return ActionResult.fail("阶段名称已存在");
            }

            ProjectPhaseInstanceEntity entity = BeanCopierUtils.copy(form, ProjectPhaseInstanceEntity.class);
            projectPhaseInstanceService.update(id, entity);
            return ActionResult.success();
        } catch (Exception e) {
            log.error("更新阶段实例失败", e);
            return ActionResult.fail("更新阶段实例失败");
        }
    }

    /**
     * 删除阶段实例
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除阶段实例")
    public ActionResult<Void> delete(@Parameter(description = "阶段实例ID") @PathVariable String id) {
        try {
            ProjectPhaseInstanceEntity existing = projectPhaseInstanceService.getInfo(id);
            if (existing == null) {
                return ActionResult.fail("阶段实例不存在");
            }

            projectPhaseInstanceService.delete(id);
            return ActionResult.success();
        } catch (Exception e) {
            log.error("删除阶段实例失败", e);
            return ActionResult.fail("删除阶段实例失败");
        }
    }

    /**
     * 批量删除阶段实例
     */
    @DeleteMapping
    @Operation(summary = "批量删除阶段实例")
    public ActionResult<Void> batchDelete(@RequestBody List<String> ids) {
        try {
            projectPhaseInstanceService.batchDelete(ids);
            return ActionResult.success();
        } catch (Exception e) {
            log.error("批量删除阶段实例失败", e);
            return ActionResult.fail("批量删除阶段实例失败");
        }
    }

    /**
     * 获取阶段实例选择列表
     */
    @GetMapping("/getSelectList")
    @Operation(summary = "获取阶段实例选择列表")
    public ActionResult<List<ProjectPhaseInstanceVO>> getSelectList(
        @Parameter(description = "项目ID") @RequestParam String projectId,
        @Parameter(description = "关键字") @RequestParam(required = false) String keyword) {
        try {
            List<ProjectPhaseInstanceEntity> list = projectPhaseInstanceService.getSelectList(projectId, keyword);
            List<ProjectPhaseInstanceVO> listVO = BeanCopierUtils.copyList(list, ProjectPhaseInstanceVO.class);
            return ActionResult.success(listVO);
        } catch (Exception e) {
            log.error("获取阶段实例选择列表失败", e);
            return ActionResult.fail("获取阶段实例选择列表失败");
        }
    }

    /**
     * 从项目模板创建阶段实例
     */
    @PostMapping("/createFromTemplate")
    @Operation(summary = "从项目模板创建阶段实例")
    public ActionResult<Integer> createFromTemplate(
        @Parameter(description = "项目ID") @RequestParam String projectId,
        @Parameter(description = "项目模板ID") @RequestParam String templateId) {
        try {
            int count = projectPhaseInstanceService.createFromTemplate(projectId, templateId);
            return ActionResult.success(count);
        } catch (Exception e) {
            log.error("从项目模板创建阶段实例失败", e);
            return ActionResult.fail("从项目模板创建阶段实例失败");
        }
    }

    /**
     * 更新阶段状态
     */
    @PutMapping("/{id}/status")
    @Operation(summary = "更新阶段状态")
    public ActionResult<Void> updateStatus(
        @Parameter(description = "阶段实例ID") @PathVariable String id,
        @Parameter(description = "状态ID") @RequestParam String statusId) {
        try {
            projectPhaseInstanceService.updateStatus(id, statusId);
            return ActionResult.success();
        } catch (Exception e) {
            log.error("更新阶段状态失败", e);
            return ActionResult.fail("更新阶段状态失败");
        }
    }

    /**
     * 开始阶段
     */
    @PostMapping("/{id}/start")
    @Operation(summary = "开始阶段")
    public ActionResult<Void> startPhase(@Parameter(description = "阶段实例ID") @PathVariable String id) {
        try {
            projectPhaseInstanceService.startPhase(id);
            return ActionResult.success();
        } catch (Exception e) {
            log.error("开始阶段失败", e);
            return ActionResult.fail("开始阶段失败");
        }
    }

    /**
     * 完成阶段
     */
    @PostMapping("/{id}/complete")
    @Operation(summary = "完成阶段")
    public ActionResult<Void> completePhase(@Parameter(description = "阶段实例ID") @PathVariable String id) {
        try {
            projectPhaseInstanceService.completePhase(id);
            return ActionResult.success();
        } catch (Exception e) {
            log.error("完成阶段失败", e);
            return ActionResult.fail("完成阶段失败");
        }
    }

    /**
     * 获取阶段进度统计
     */
    @GetMapping("/progressStats")
    @Operation(summary = "获取阶段进度统计")
    public ActionResult<Map<String, Object>> getPhaseProgressStats(
        @Parameter(description = "项目ID") @RequestParam String projectId) {
        try {
            Map<String, Object> stats = projectPhaseInstanceService.getPhaseProgressStats(projectId);
            return ActionResult.success(stats);
        } catch (Exception e) {
            log.error("获取阶段进度统计失败", e);
            return ActionResult.fail("获取阶段进度统计失败");
        }
    }

    /**
     * 调整序号
     */
    @PostMapping("/{id}/adjustSeqNo")
    @Operation(summary = "调整序号")
    public ActionResult<Void> adjustSeqNo(
        @Parameter(description = "阶段实例ID") @PathVariable String id,
        @Parameter(description = "方向(up/down)") @RequestParam String direction) {
        try {
            projectPhaseInstanceService.adjustSeqNo(id, direction);
            return ActionResult.success();
        } catch (Exception e) {
            log.error("调整序号失败", e);
            return ActionResult.fail("调整序号失败");
        }
    }
}