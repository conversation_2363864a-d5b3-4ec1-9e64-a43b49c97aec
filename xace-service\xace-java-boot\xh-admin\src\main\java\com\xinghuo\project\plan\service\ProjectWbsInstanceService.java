package com.xinghuo.project.plan.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.plan.entity.ProjectWbsInstanceEntity;
import com.xinghuo.project.plan.model.wbs.ProjectWbsInstancePagination;

import java.util.List;
import java.util.Map;

/**
 * 项目实际WBS计划服务接口
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface ProjectWbsInstanceService extends BaseService<ProjectWbsInstanceEntity> {

    /**
     * 获取项目WBS计划列表
     *
     * @param pagination 分页参数
     * @return WBS计划列表
     */
    List<ProjectWbsInstanceEntity> getList(ProjectWbsInstancePagination pagination);

    /**
     * 根据项目ID获取WBS计划列表
     *
     * @param projectId 项目ID
     * @return WBS计划列表
     */
    List<ProjectWbsInstanceEntity> getListByProjectId(String projectId);

    /**
     * 根据父级节点ID获取子节点列表
     *
     * @param parentId 父级节点ID
     * @return 子节点列表
     */
    List<ProjectWbsInstanceEntity> getListByParentId(String parentId);

    /**
     * 获取WBS计划详情
     *
     * @param id WBS节点ID
     * @return WBS计划信息
     */
    ProjectWbsInstanceEntity getInfo(String id);

    /**
     * 创建WBS计划
     *
     * @param entity WBS计划信息
     * @return WBS节点ID
     */
    String create(ProjectWbsInstanceEntity entity);

    /**
     * 更新WBS计划
     *
     * @param id WBS节点ID
     * @param entity WBS计划信息
     */
    void update(String id, ProjectWbsInstanceEntity entity);

    /**
     * 删除WBS计划（级联删除子节点）
     *
     * @param id WBS节点ID
     */
    void delete(String id);

    /**
     * 批量删除WBS计划
     *
     * @param ids WBS节点ID列表
     */
    void batchDelete(List<String> ids);

    /**
     * 根据项目ID删除所有WBS计划
     *
     * @param projectId 项目ID
     */
    void deleteByProjectId(String projectId);

    /**
     * 检查WBS编码在指定项目中是否已存在
     *
     * @param projectId 项目ID
     * @param wbsCode WBS编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByWbsCode(String projectId, String wbsCode, String excludeId);

    /**
     * 根据源模板WBS ID获取WBS计划
     *
     * @param projectId 项目ID
     * @param sourceSchemaWbsId 源模板WBS ID
     * @return WBS计划信息
     */
    ProjectWbsInstanceEntity getBySourceSchemaWbsId(String projectId, String sourceSchemaWbsId);

    /**
     * 获取WBS计划选择列表
     *
     * @param projectId 项目ID
     * @param keyword 关键字
     * @return WBS计划列表
     */
    List<ProjectWbsInstanceEntity> getSelectList(String projectId, String keyword);

    /**
     * 从项目模板创建WBS计划
     *
     * @param projectId 项目ID
     * @param templateId 项目模板ID
     * @return 创建的WBS节点数量
     */
    int createFromTemplate(String projectId, String templateId);

    /**
     * 批量保存WBS计划
     *
     * @param projectId 项目ID
     * @param wbsInstances WBS计划列表
     */
    void batchSave(String projectId, List<ProjectWbsInstanceEntity> wbsInstances);

    /**
     * 更新序号
     *
     * @param id WBS节点ID
     * @param seqNo 序号
     */
    void updateSeqNo(String id, Integer seqNo);

    /**
     * 批量更新序号
     *
     * @param seqNoMap 序号映射 (ID -> 序号)
     */
    void batchUpdateSeqNo(Map<String, Integer> seqNoMap);

    /**
     * 更新任务状态
     *
     * @param id WBS节点ID
     * @param statusId 状态ID
     */
    void updateStatus(String id, String statusId);

    /**
     * 更新完成进度
     *
     * @param id WBS节点ID
     * @param progress 完成百分比
     */
    void updateProgress(String id, Integer progress);

    /**
     * 开始任务
     *
     * @param id WBS节点ID
     */
    void startTask(String id);

    /**
     * 完成任务
     *
     * @param id WBS节点ID
     */
    void completeTask(String id);

    /**
     * 获取WBS树形结构
     *
     * @param projectId 项目ID
     * @return WBS树形结构
     */
    List<ProjectWbsInstanceEntity> getWbsTree(String projectId);

    /**
     * 获取任务进度统计
     *
     * @param projectId 项目ID
     * @return 进度统计信息
     */
    Map<String, Object> getTaskProgressStats(String projectId);

    /**
     * 获取里程碑列表
     *
     * @param projectId 项目ID
     * @return 里程碑列表
     */
    List<ProjectWbsInstanceEntity> getMilestoneList(String projectId);

    /**
     * 获取关键路径
     *
     * @param projectId 项目ID
     * @return 关键路径任务列表
     */
    List<ProjectWbsInstanceEntity> getCriticalPath(String projectId);

    /**
     * 更新前置任务关系
     *
     * @param id WBS节点ID
     * @param predecessors 前置任务ID列表
     */
    void updatePredecessors(String id, String predecessors);

    /**
     * 获取下一个序号
     *
     * @param parentId 父级节点ID
     * @return 下一个序号
     */
    Integer getNextSeqNo(String parentId);

    /**
     * 调整序号（上移/下移）
     *
     * @param id WBS节点ID
     * @param direction 方向（up/down）
     */
    void adjustSeqNo(String id, String direction);
}