package com.xinghuo.project.plan.model.wbs;

import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 项目实际WBS计划分页查询参数
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "项目实际WBS计划分页查询参数")
public class ProjectWbsInstancePagination extends Pagination {

    /**
     * 项目ID
     */
    @Schema(description = "项目ID")
    private String projectId;

    /**
     * 源项目模板WBS明细ID
     */
    @Schema(description = "源项目模板WBS明细ID")
    private String sourceSchemaWbsId;

    /**
     * 父级节点ID
     */
    @Schema(description = "父级节点ID")
    private String parentId;

    /**
     * WBS编码
     */
    @Schema(description = "WBS编码")
    private String wbsCode;

    /**
     * 层级深度最小值
     */
    @Schema(description = "层级深度最小值")
    private Integer levelMin;

    /**
     * 层级深度最大值
     */
    @Schema(description = "层级深度最大值")
    private Integer levelMax;

    /**
     * 节点类型
     */
    @Schema(description = "节点类型")
    private Integer nodeType;

    /**
     * 是否是里程碑
     */
    @Schema(description = "是否是里程碑")
    private Integer isMilestone;

    /**
     * 计划开始日期开始
     */
    @Schema(description = "计划开始日期开始")
    private Date planStartDateStart;

    /**
     * 计划开始日期结束
     */
    @Schema(description = "计划开始日期结束")
    private Date planStartDateEnd;

    /**
     * 计划结束日期开始
     */
    @Schema(description = "计划结束日期开始")
    private Date planEndDateStart;

    /**
     * 计划结束日期结束
     */
    @Schema(description = "计划结束日期结束")
    private Date planEndDateEnd;

    /**
     * 计划工期最小值
     */
    @Schema(description = "计划工期最小值（天）")
    private BigDecimal planDurationMin;

    /**
     * 计划工期最大值
     */
    @Schema(description = "计划工期最大值（天）")
    private BigDecimal planDurationMax;

    /**
     * 计划工时最小值
     */
    @Schema(description = "计划工时最小值（小时）")
    private BigDecimal planHourMin;

    /**
     * 计划工时最大值
     */
    @Schema(description = "计划工时最大值（小时）")
    private BigDecimal planHourMax;

    /**
     * 约束类型ID
     */
    @Schema(description = "约束类型ID")
    private String constraintTypeId;

    /**
     * 实际开始日期开始
     */
    @Schema(description = "实际开始日期开始")
    private Date actualStartDateStart;

    /**
     * 实际开始日期结束
     */
    @Schema(description = "实际开始日期结束")
    private Date actualStartDateEnd;

    /**
     * 实际结束日期开始
     */
    @Schema(description = "实际结束日期开始")
    private Date actualEndDateStart;

    /**
     * 实际结束日期结束
     */
    @Schema(description = "实际结束日期结束")
    private Date actualEndDateEnd;

    /**
     * 完成百分比最小值
     */
    @Schema(description = "完成百分比最小值")
    private Integer progressMin;

    /**
     * 完成百分比最大值
     */
    @Schema(description = "完成百分比最大值")
    private Integer progressMax;

    /**
     * 任务状态ID
     */
    @Schema(description = "任务状态ID")
    private String statusId;

    /**
     * 责任人ID
     */
    @Schema(description = "责任人ID")
    private String responseUserId;

    /**
     * 完成方式ID
     */
    @Schema(description = "完成方式ID")
    private String completeTypeId;

    /**
     * 确认人ID
     */
    @Schema(description = "确认人ID")
    private String confirmUserId;

    /**
     * 创建时间开始
     */
    @Schema(description = "创建时间开始")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    @Schema(description = "创建时间结束")
    private LocalDateTime createTimeEnd;

    /**
     * 更新时间开始
     */
    @Schema(description = "更新时间开始")
    private LocalDateTime updateTimeStart;

    /**
     * 更新时间结束
     */
    @Schema(description = "更新时间结束")
    private LocalDateTime updateTimeEnd;

    /**
     * 关键字搜索（WBS编码、名称等）
     */
    @Schema(description = "关键字搜索")
    private String keyword;
}