package com.xinghuo.project.ticket.model;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

/**
 * 项目实际问题实例视图对象
 */
@Data
@Schema(description = "项目实际问题实例视图对象")
public class ProjectInsIssueVO {
    
    @Schema(description = "问题实例ID")
    private String id;
    
    @Schema(description = "所属的实际项目ID")
    private String projectId;
    
    @Schema(description = "项目名称")
    private String projectName;
    
    @Schema(description = "源自哪个标准问题库ID")
    private String sourceLibraryIssueId;
    
    @Schema(description = "源问题标题")
    private String sourceIssueTitle;
    
    @Schema(description = "问题标题")
    private String title;
    
    @Schema(description = "问题描述")
    private String description;
    
    @Schema(description = "问题分类ID")
    private String issueCategoryId;
    
    @Schema(description = "问题分类名称")
    private String issueCategoryName;
    
    @Schema(description = "优先级ID")
    private String priorityId;
    
    @Schema(description = "优先级名称")
    private String priorityName;
    
    @Schema(description = "解决者用户ID")
    private String solverUserId;
    
    @Schema(description = "解决者姓名")
    private String solverUserName;
    
    @Schema(description = "截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dueDate;
    
    @Schema(description = "解决方案")
    private String solution;
    
    @Schema(description = "状态ID")
    private String statusId;
    
    @Schema(description = "状态名称")
    private String statusName;
    
    @Schema(description = "日志记录")
    private String log;
    
    @Schema(description = "关联任务ID")
    private String relatedTaskId;
    
    @Schema(description = "关联任务名称")
    private String relatedTaskName;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;
    
    @Schema(description = "创建人")
    private String createdBy;
    
    @Schema(description = "最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdatedAt;
    
    @Schema(description = "最后更新人")
    private String lastUpdatedBy;
}