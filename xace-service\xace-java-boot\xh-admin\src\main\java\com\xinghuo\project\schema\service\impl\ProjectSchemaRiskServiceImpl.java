package com.xinghuo.project.schema.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.exception.DataException;
import com.xinghuo.project.schema.dao.ProjectSchemaRiskMapper;
import com.xinghuo.project.schema.entity.ProjectSchemaRiskEntity;
import com.xinghuo.project.schema.model.ProjectSchemaRiskPagination;
import com.xinghuo.project.schema.model.vo.ProjectSchemaRiskVO;
import com.xinghuo.project.schema.service.ProjectSchemaRiskService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 项目模板风险清单服务实现类
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Slf4j
@Service
public class ProjectSchemaRiskServiceImpl extends BaseServiceImpl<ProjectSchemaRiskMapper, ProjectSchemaRiskEntity> implements ProjectSchemaRiskService {

    @Override
    public List<ProjectSchemaRiskVO> getList(ProjectSchemaRiskPagination pagination) {
        LambdaQueryWrapper<ProjectSchemaRiskEntity> queryWrapper = new LambdaQueryWrapper<>();
        
        // 项目模板ID条件
        if (StrXhUtil.isNotEmpty(pagination.getProjectTemplateId())) {
            queryWrapper.eq(ProjectSchemaRiskEntity::getProjectTemplateId, pagination.getProjectTemplateId());
        }
        
        // 标准风险库ID条件
        if (StrXhUtil.isNotEmpty(pagination.getLibraryRiskId())) {
            queryWrapper.eq(ProjectSchemaRiskEntity::getLibraryRiskId, pagination.getLibraryRiskId());
        }
        
        // 是否必须识别条件
        if (pagination.getIsRequired() != null) {
            queryWrapper.eq(ProjectSchemaRiskEntity::getIsRequired, pagination.getIsRequired());
        }
        
        // TODO: 关键字搜索需要根据实际的风险库表结构来实现
        // if (StrXhUtil.isNotEmpty(pagination.getKeyword())) {
        //     queryWrapper.and(wrapper -> wrapper
        //         .like(related_field_from_risk_library, pagination.getKeyword())
        //     );
        // }
        
        // 排序
        queryWrapper.orderByDesc(ProjectSchemaRiskEntity::getCreatedAt);
        
        List<ProjectSchemaRiskEntity> list = this.list(queryWrapper);
        return BeanCopierUtils.copyList(list, ProjectSchemaRiskVO.class);
    }

    @Override
    public List<ProjectSchemaRiskEntity> getListByProjectTemplateId(String projectTemplateId) {
        LambdaQueryWrapper<ProjectSchemaRiskEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectSchemaRiskEntity::getProjectTemplateId, projectTemplateId)
                   .orderByDesc(ProjectSchemaRiskEntity::getCreatedAt);
        return this.list(queryWrapper);
    }

    @Override
    public List<ProjectSchemaRiskEntity> getRequiredListByProjectTemplateId(String projectTemplateId) {
        LambdaQueryWrapper<ProjectSchemaRiskEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectSchemaRiskEntity::getProjectTemplateId, projectTemplateId)
                   .eq(ProjectSchemaRiskEntity::getIsRequired, 1)
                   .orderByDesc(ProjectSchemaRiskEntity::getCreatedAt);
        return this.list(queryWrapper);
    }

    @Override
    public ProjectSchemaRiskVO getDetailInfo(String id) {
        ProjectSchemaRiskEntity entity = this.getById(id);
        if (entity == null) {
            throw new DataException("风险清单不存在");
        }
        return BeanCopierUtils.copy(entity, ProjectSchemaRiskVO.class);
    }

    @Override
    public ProjectSchemaRiskEntity getInfo(String id) {
        ProjectSchemaRiskEntity entity = this.getById(id);
        if (entity == null) {
            throw new DataException("风险清单不存在");
        }
        return entity;
    }

    @Override
    @Transactional
    public String create(ProjectSchemaRiskVO riskVO) {
        // 检查风险是否已存在
        if (isExistByLibraryRiskId(riskVO.getProjectTemplateId(), riskVO.getLibraryRiskId(), null)) {
            throw new DataException("该项目模板中已存在此风险项");
        }
        
        ProjectSchemaRiskEntity entity = BeanCopierUtils.copy(riskVO, ProjectSchemaRiskEntity.class);
        
        // 设置默认值
        if (entity.getIsRequired() == null) {
            entity.setIsRequired(1);
        }
        
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional
    public void update(String id, ProjectSchemaRiskVO riskVO) {
        ProjectSchemaRiskEntity entity = this.getById(id);
        if (entity == null) {
            throw new DataException("风险清单不存在");
        }
        
        // 检查风险是否已存在
        if (isExistByLibraryRiskId(riskVO.getProjectTemplateId(), riskVO.getLibraryRiskId(), id)) {
            throw new DataException("该项目模板中已存在此风险项");
        }
        
        entity.setProjectTemplateId(riskVO.getProjectTemplateId());
        entity.setLibraryRiskId(riskVO.getLibraryRiskId());
        entity.setIsRequired(riskVO.getIsRequired());
        this.updateById(entity);
    }

    @Override
    @Transactional
    public void delete(String id) {
        ProjectSchemaRiskEntity entity = this.getById(id);
        if (entity == null) {
            throw new DataException("风险清单不存在");
        }
        this.removeById(id);
    }

    @Override
    @Transactional
    public void batchDelete(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        this.removeByIds(ids);
    }

    @Override
    @Transactional
    public void updateRequired(String id, Integer isRequired) {
        UpdateWrapper<ProjectSchemaRiskEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("f_id", id)
                    .set("is_required", isRequired);
        this.update(updateWrapper);
    }

    @Override
    @Transactional
    public void batchUpdateRequired(List<String> ids, Integer isRequired) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        UpdateWrapper<ProjectSchemaRiskEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("f_id", ids)
                    .set("is_required", isRequired);
        this.update(updateWrapper);
    }

    @Override
    public boolean isExistByLibraryRiskId(String projectTemplateId, String libraryRiskId, String excludeId) {
        LambdaQueryWrapper<ProjectSchemaRiskEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectSchemaRiskEntity::getProjectTemplateId, projectTemplateId)
                   .eq(ProjectSchemaRiskEntity::getLibraryRiskId, libraryRiskId);
        if (StrXhUtil.isNotEmpty(excludeId)) {
            queryWrapper.ne(ProjectSchemaRiskEntity::getId, excludeId);
        }
        return this.count(queryWrapper) > 0;
    }

    @Override
    @Transactional
    public void importFromLibrary(String projectTemplateId, List<String> libraryRiskIds) {
        if (libraryRiskIds == null || libraryRiskIds.isEmpty()) {
            return;
        }
        
        List<ProjectSchemaRiskEntity> entities = new ArrayList<>();
        for (String libraryRiskId : libraryRiskIds) {
            // 检查是否已存在
            if (!isExistByLibraryRiskId(projectTemplateId, libraryRiskId, null)) {
                ProjectSchemaRiskEntity entity = new ProjectSchemaRiskEntity();
                entity.setProjectTemplateId(projectTemplateId);
                entity.setLibraryRiskId(libraryRiskId);
                entity.setIsRequired(1); // 默认为必须识别
                entities.add(entity);
            }
        }
        
        if (!entities.isEmpty()) {
            this.saveBatch(entities);
        }
    }

    @Override
    @Transactional
    public void copyRisks(String sourceProjectTemplateId, String targetProjectTemplateId) {
        List<ProjectSchemaRiskEntity> sourceList = getListByProjectTemplateId(sourceProjectTemplateId);
        if (sourceList.isEmpty()) {
            return;
        }
        
        List<ProjectSchemaRiskEntity> targetList = new ArrayList<>();
        for (ProjectSchemaRiskEntity source : sourceList) {
            // 检查目标模板是否已存在此风险
            if (!isExistByLibraryRiskId(targetProjectTemplateId, source.getLibraryRiskId(), null)) {
                ProjectSchemaRiskEntity target = BeanCopierUtils.copy(source, ProjectSchemaRiskEntity.class);
                target.setId(null);
                target.setProjectTemplateId(targetProjectTemplateId);
                targetList.add(target);
            }
        }
        
        if (!targetList.isEmpty()) {
            this.saveBatch(targetList);
        }
    }

    @Override
    @Transactional
    public void deleteByProjectTemplateId(String projectTemplateId) {
        LambdaQueryWrapper<ProjectSchemaRiskEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectSchemaRiskEntity::getProjectTemplateId, projectTemplateId);
        this.remove(queryWrapper);
    }

    @Override
    @Transactional
    public void deleteByLibraryRiskId(String libraryRiskId) {
        LambdaQueryWrapper<ProjectSchemaRiskEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectSchemaRiskEntity::getLibraryRiskId, libraryRiskId);
        this.remove(queryWrapper);
    }

    @Override
    @Transactional
    public void batchAddRisks(String projectTemplateId, List<String> libraryRiskIds, Integer isRequired) {
        if (libraryRiskIds == null || libraryRiskIds.isEmpty()) {
            return;
        }
        
        List<ProjectSchemaRiskEntity> entities = new ArrayList<>();
        for (String libraryRiskId : libraryRiskIds) {
            // 检查是否已存在
            if (!isExistByLibraryRiskId(projectTemplateId, libraryRiskId, null)) {
                ProjectSchemaRiskEntity entity = new ProjectSchemaRiskEntity();
                entity.setProjectTemplateId(projectTemplateId);
                entity.setLibraryRiskId(libraryRiskId);
                entity.setIsRequired(isRequired != null ? isRequired : 1);
                entities.add(entity);
            }
        }
        
        if (!entities.isEmpty()) {
            this.saveBatch(entities);
        }
    }

    @Override
    @Transactional
    public void syncLibraryRiskChanges(String libraryRiskId) {
        // TODO: 实现标准风险库变更同步逻辑
        // 这里需要根据实际的风险库表结构来实现
        log.info("同步标准风险库变更: {}", libraryRiskId);
    }
}