package com.xinghuo.project.schema.model.vo;

import com.xinghuo.project.schema.entity.ProjectSchemaRiskEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目模板风险清单VO类
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectSchemaRiskVO extends ProjectSchemaRiskEntity {

    /**
     * 项目模板名称（冗余字段，便于显示）
     */
    private String projectTemplateName;

    /**
     * 标准风险库名称（冗余字段，便于显示）
     */
    private String libraryRiskName;

    /**
     * 风险描述（来自风险库）
     */
    private String riskDescription;

    /**
     * 风险级别（来自风险库）
     */
    private String riskLevel;

    /**
     * 风险类别（来自风险库）
     */
    private String riskCategory;

    /**
     * 是否是必须识别的风险显示文本
     */
    private String isRequiredText;

    /**
     * 应对策略（来自风险库）
     */
    private String mitigation;

    /**
     * 风险影响（来自风险库）
     */
    private String impact;

    /**
     * 风险概率（来自风险库）
     */
    private String probability;
}