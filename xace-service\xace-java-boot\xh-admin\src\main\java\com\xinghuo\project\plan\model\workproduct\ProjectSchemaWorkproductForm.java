package com.xinghuo.project.plan.model.workproduct;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 项目模板交付物计划表单对象
 * 用于交付物计划的创建和更新
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@Schema(description = "项目模板交付物计划表单对象")
public class ProjectSchemaWorkproductForm {

    /**
     * 主键ID（更新时必填）
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 所属项目模板ID
     */
    @NotBlank(message = "项目模板ID不能为空")
    @Schema(description = "所属项目模板ID")
    private String projectTemplateId;

    /**
     * 源自哪个标准交付物库ID
     */
    @Schema(description = "标准交付物库ID")
    private String libraryWorkproductId;

    /**
     * 交付物名称
     */
    @NotBlank(message = "交付物名称不能为空")
    @Schema(description = "交付物名称")
    private String name;

    /**
     * 描述/验收标准
     */
    @Schema(description = "描述/验收标准")
    private String description;

    /**
     * 交付物类型ID
     */
    @NotBlank(message = "交付物类型ID不能为空")
    @Schema(description = "交付物类型ID")
    private String typeId;

    /**
     * 交付物子类型ID
     */
    @Schema(description = "交付物子类型ID")
    private String subTypeId;

    /**
     * 显示顺序
     */
    @Schema(description = "显示顺序")
    private Integer seqNo;

    /**
     * 是否是必需交付物 (1:是, 0:否)
     */
    @NotNull(message = "是否必需不能为空")
    @Schema(description = "是否是必需交付物")
    private Integer isRequired;

    /**
     * 责任角色ID
     */
    @Schema(description = "责任角色ID")
    private String responseRoleId;

    /**
     * 是否需要评审
     */
    @NotNull(message = "是否需要评审不能为空")
    @Schema(description = "是否需要评审")
    private Integer reviewRequired;

    /**
     * 关联的项目模板阶段ID
     */
    @Schema(description = "关联的项目模板阶段ID")
    private String schemaPhaseId;

    /**
     * 关联的项目模板WBS节点ID
     */
    @Schema(description = "关联的项目模板WBS节点ID")
    private String schemaWbsId;
}