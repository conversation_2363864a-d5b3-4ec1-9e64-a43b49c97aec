package com.xinghuo.project.ticket.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import java.util.Date;

/**
 * 项目实际风险实例实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_ins_risk")
public class ProjectInsRiskEntity extends BaseEntityV2.CUBaseEntityV2<String> {
    
    /**
     * 所属的实际项目ID
     */
    @TableField("PROJECT_ID")
    private String projectId;
    
    /**
     * 源自哪个标准风险库ID
     */
    @TableField("SOURCE_LIBRARY_RISK_ID")
    private String sourceLibraryRiskId;
    
    /**
     * 风险标题
     */
    @TableField("TITLE")
    private String title;
    
    /**
     * 风险描述
     */
    @TableField("DESCRIPTION")
    private String description;
    
    /**
     * 风险分类ID
     */
    @TableField("RISK_CATEGORY_ID")
    private String riskCategoryId;
    
    /**
     * 概率等级ID
     */
    @TableField("PROBABILITY_LEVEL_ID")
    private String probabilityLevelId;
    
    /**
     * 影响等级ID
     */
    @TableField("IMPACT_LEVEL_ID")
    private String impactLevelId;
    
    /**
     * 风险评分
     */
    @TableField("RISK_SCORE")
    private Integer riskScore;
    
    /**
     * 应对策略
     */
    @TableField("STRATEGY")
    private String strategy;
    
    /**
     * 应对措施
     */
    @TableField("ACTIONS")
    private String actions;
    
    /**
     * 负责人用户ID
     */
    @TableField("RESPONSE_USER_ID")
    private String responseUserId;
    
    /**
     * 截止日期
     */
    @TableField("DUE_DATE")
    private Date dueDate;
    
    /**
     * 状态ID
     */
    @TableField("STATUS_ID")
    private String statusId;
    
    /**
     * 日志记录
     */
    @TableField("LOG")
    private String log;
}