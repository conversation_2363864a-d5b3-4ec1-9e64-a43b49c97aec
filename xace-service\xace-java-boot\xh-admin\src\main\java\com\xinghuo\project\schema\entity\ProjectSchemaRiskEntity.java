package com.xinghuo.project.schema.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目模板风险清单实体类
 * 对应数据库表：zz_proj_schema_risk
 *
 * 项目模板的风险清单配置，关联标准风险库，用于在项目模板中预设需要识别的风险项。
 * 可以设置某些风险为模板默认必须识别的风险。
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_schema_risk")
public class ProjectSchemaRiskEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 所属项目模板ID (关联 zz_proj_template)
     */
    @TableField("project_template_id")
    private String projectTemplateId;

    /**
     * 关联的标准风险库ID (关联 zz_proj_tpl_risk_library)
     */
    @TableField("library_risk_id")
    private String libraryRiskId;

    /**
     * 是否是模板默认必须识别的风险 (1:是, 0:否)
     */
    @TableField("is_required")
    private Integer isRequired;
}