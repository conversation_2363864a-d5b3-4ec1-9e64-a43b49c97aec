package com.xinghuo.project.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.permission.entity.UserEntity;
import com.xinghuo.permission.service.UserService;
import com.xinghuo.project.biz.dao.PaymentContractMapper;
import com.xinghuo.project.biz.dao.PaymentContractMoneyMapper;
import com.xinghuo.project.biz.entity.BizContractEntity;
import com.xinghuo.project.biz.entity.PaymentContractEntity;
import com.xinghuo.project.biz.entity.PaymentContractMoneyEntity;
import com.xinghuo.project.biz.entity.SupplierEntity;
import com.xinghuo.project.biz.model.paymentContract.PaymentContractPagination;
import com.xinghuo.project.biz.model.paymentContract.PaymentContractVO;
import com.xinghuo.project.biz.service.BizContractService;
import com.xinghuo.project.biz.service.PaymentContractService;
import com.xinghuo.project.biz.service.SupplierService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 付款合同服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-17
 */
@Slf4j
@Service
public class PaymentContractServiceImpl extends BaseServiceImpl<PaymentContractMapper, PaymentContractEntity> implements PaymentContractService {

    @Autowired
    private BizContractService bizContractService;

    @Autowired
    private SupplierService supplierService;

    @Autowired
    private UserService userService;

    @Autowired
    private PaymentContractMoneyMapper paycontractMoneyMapper;

    @Override
    public List<PaymentContractEntity> getList(PaymentContractPagination pagination) {
        QueryWrapper<PaymentContractEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<PaymentContractEntity> lambda = queryWrapper.lambda();

        // 根据采购合同名称模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getName())) {
            lambda.like(PaymentContractEntity::getName, pagination.getName());
        }

        // 根据采购合同编号模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getCno())) {
            lambda.like(PaymentContractEntity::getCno, pagination.getCno());
        }

        // 根据收款合同ID精确查询
        if (StrXhUtil.isNotEmpty(pagination.getContractId())) {
            lambda.eq(PaymentContractEntity::getContractId, pagination.getContractId());
        }

        // 根据供应商ID精确查询
        if (StrXhUtil.isNotEmpty(pagination.getSuppilerId())) {
            lambda.eq(PaymentContractEntity::getSuppilerId, pagination.getSuppilerId());
        }

        // 根据采购负责人ID精确查询
        if (StrXhUtil.isNotEmpty(pagination.getOwnId())) {
            lambda.eq(PaymentContractEntity::getOwnId, pagination.getOwnId());
        }

        // 根据采购合同状态精确查询
        if (StrXhUtil.isNotEmpty(pagination.getStatus())) {
            lambda.eq(PaymentContractEntity::getStatus, pagination.getStatus());
        }

        // 根据付款状态精确查询
        if (StrXhUtil.isNotEmpty(pagination.getMoneyStatus())) {
            lambda.eq(PaymentContractEntity::getMoneyStatus, pagination.getMoneyStatus());
        }

        // 根据签订年份精确查询
        if (pagination.getSignYear() != null) {
            lambda.eq(PaymentContractEntity::getSignYear, pagination.getSignYear());
        }

        // 根据金额范围查询
        if (pagination.getMinAmount() != null) {
            lambda.ge(PaymentContractEntity::getAmount, pagination.getMinAmount());
        }
        if (pagination.getMaxAmount() != null) {
            lambda.le(PaymentContractEntity::getAmount, pagination.getMaxAmount());
        }

        // 根据签订日期范围查询
        if (pagination.getSignDateStart() != null) {
            lambda.ge(PaymentContractEntity::getSignDate, pagination.getSignDateStart());
        }
        if (pagination.getSignDateEnd() != null) {
            lambda.le(PaymentContractEntity::getSignDate, pagination.getSignDateEnd());
        }

        // 根据关键字搜索采购合同名称或编号
        String keyword = pagination.getKeyword();
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.and(wrapper -> wrapper
                    .like(PaymentContractEntity::getName, keyword)
                    .or()
                    .like(PaymentContractEntity::getCno, keyword)
            );
        }

        // 排除已删除的记录
//        lambda.isNull(PaymentContractEntity::getDeleteMark);

        // 排序
        lambda.orderByDesc(PaymentContractEntity::getCreateTime);
        return processDataType(queryWrapper,pagination);

        // 分页
    }

    @Override
    public List<PaymentContractEntity> getListByContractId(String contractId) {
        LambdaQueryWrapper<PaymentContractEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PaymentContractEntity::getContractId, contractId);
        queryWrapper.eq(PaymentContractEntity::getDeleteMark, 0);
        queryWrapper.orderByDesc(PaymentContractEntity::getCreateTime);
        return this.list(queryWrapper);
    }

    @Override
    public List<PaymentContractEntity> getListBySupplierId(String supplierId) {
        LambdaQueryWrapper<PaymentContractEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PaymentContractEntity::getSuppilerId, supplierId);
        queryWrapper.eq(PaymentContractEntity::getDeleteMark, 0);
        queryWrapper.orderByDesc(PaymentContractEntity::getCreateTime);
        return this.list(queryWrapper);
    }

    @Override
    public PaymentContractEntity getInfo(String id) {
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(PaymentContractEntity entity) {
        entity.setId(RandomUtil.snowId());
        entity.setDeleteMark(0);

        // 设置初始状态
        if (StrXhUtil.isEmpty(entity.getStatus())) {
            entity.setStatus("draft"); // 草稿
        }
        if (StrXhUtil.isEmpty(entity.getMoneyStatus())) {
            entity.setMoneyStatus("unpaid"); // 未付款
        }

        // 设置初始金额
        if (entity.getYfAmount() == null) {
            entity.setYfAmount(BigDecimal.ZERO);
        }

        this.save(entity);

        // 更新收款合同的外采信息
        updateContractExternalAmount(entity.getContractId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, PaymentContractEntity entity) {
        PaymentContractEntity oldEntity = this.getById(id);
        if (oldEntity == null) {
            throw new RuntimeException("采购合同不存在");
        }

        String oldContractId = oldEntity.getContractId();
        entity.setId(id);

        this.updateById(entity);

        // 如果收款合同ID发生变化，需要更新两个收款合同的外采信息
        if (!oldContractId.equals(entity.getContractId())) {
            updateContractExternalAmount(oldContractId);
        }
        updateContractExternalAmount(entity.getContractId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        PaymentContractEntity entity = this.getById(id);
        if (entity == null) {
            throw new RuntimeException("采购合同不存在");
        }

        // 逻辑删除
        entity.setDeleteMark(1);
        this.updateById(entity);

        // 更新收款合同的外采信息
        updateContractExternalAmount(entity.getContractId());
    }

    @Override
    public boolean isExistByCNo(String cNo, String id) {
        if (StrXhUtil.isEmpty(cNo)) {
            return false;
        }

        LambdaQueryWrapper<PaymentContractEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PaymentContractEntity::getCno, cNo);
        queryWrapper.eq(PaymentContractEntity::getDeleteMark, 0);

        // 如果是更新操作，需要排除自身
        if (StrXhUtil.isNotEmpty(id)) {
            queryWrapper.ne(PaymentContractEntity::getId, id);
        }

        return this.count(queryWrapper) > 0;
    }

    @Override
    public void updateStatus(String id, String status) {
        PaymentContractEntity entity = new PaymentContractEntity();
        entity.setId(id);
        entity.setStatus(status);
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sign(String id, String cNo, Date signDate) {
        PaymentContractEntity entity = this.getById(id);
        if (entity == null) {
            throw new RuntimeException("采购合同不存在");
        }

        // 检查合同编号是否已存在
        if (StrXhUtil.isNotEmpty(cNo) && isExistByCNo(cNo, id)) {
            throw new RuntimeException("采购合同编号已存在");
        }

        // 更新采购合同信息
        entity.setCno(cNo);
        entity.setSignDate(signDate);
        entity.setStatus("executing"); // 执行中

        // 设置签订年份
        if (signDate != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(signDate);
            entity.setSignYear(calendar.get(Calendar.YEAR));
        }

        this.updateById(entity);

        // 更新收款合同的外采信息
        updateContractExternalAmount(entity.getContractId());
    }

    @Override
    public boolean canDelete(String id) {
        // 检查是否有关联的付款记录
        LambdaQueryWrapper<PaymentContractMoneyEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PaymentContractMoneyEntity::getPayContractId, id);

        // 如果有付款记录，则不能删除
        return paycontractMoneyMapper.selectCount(queryWrapper) == 0;
    }

    /**
     * 更新收款合同的外采信息
     *
     * @param contractId 收款合同ID
     */
    private void updateContractExternalAmount(String contractId) {
        BizContractEntity contract = bizContractService.getById(contractId);
        if (contract == null) {
            return;
        }

        // 获取该收款合同下的所有采购合同
        List<PaymentContractEntity> paycontracts = getListByContractId(contractId);

        // 计算外采总额
        BigDecimal externalAmount = BigDecimal.ZERO;
        BigDecimal outYbAmount = BigDecimal.ZERO;
        BigDecimal outEbAmount = BigDecimal.ZERO;
        BigDecimal outOtherAmount = BigDecimal.ZERO;

        for (PaymentContractEntity paycontract : paycontracts) {
            if (paycontract.getAmount() != null) {
                externalAmount = externalAmount.add(paycontract.getAmount());
            }

            // 计算各部门外采金额
            if (paycontract.getKfybAmount() != null) {
                outYbAmount = outYbAmount.add(paycontract.getKfybAmount());
            }
            if (paycontract.getKfebAmount() != null) {
                outEbAmount = outEbAmount.add(paycontract.getKfebAmount());
            }
            if (paycontract.getOtherAmount() != null) {
                outOtherAmount = outOtherAmount.add(paycontract.getOtherAmount());
            }
        }

        // 更新收款合同的外采信息
        contract.setExternalAmount(externalAmount);
        contract.setOutYbAmount(outYbAmount);
        contract.setOutEbAmount(outEbAmount);
        contract.setOutOtherAmount(outOtherAmount);

        bizContractService.updateById(contract);
    }

    @Override
    public void fillRelatedInfo(List<PaymentContractVO> listVOs) {
        for (PaymentContractVO vo : listVOs) {
            // 填充收款合同信息
            if (StrXhUtil.isNotEmpty(vo.getContractId())) {
                try {
                    BizContractEntity contract = bizContractService.getInfo(vo.getContractId());
                    if (contract != null) {
                        vo.setContractName(contract.getName());
                        vo.setContractNo(contract.getCno());
                    }
                } catch (Exception e) {
                    log.warn("获取收款合同信息失败，合同ID: {}", vo.getContractId(), e);
                }
            }

            // 填充供应商信息
            if (StrXhUtil.isNotEmpty(vo.getSuppilerId())) {
                try {
                    SupplierEntity supplier = supplierService.getInfo(vo.getSuppilerId());
                    if (supplier != null) {
                        vo.setSupplierName(supplier.getName());
                    }
                } catch (Exception e) {
                    log.warn("获取供应商信息失败，供应商ID: {}", vo.getSuppilerId());
                }
            }

            // 填充项目经理信息
            if (StrXhUtil.isNotEmpty(vo.getOwnId())) {
                try {
                    UserEntity user = userService.getInfo(vo.getOwnId());
                    if (user != null) {
                        vo.setOwnName(user.getRealName());
                    }
                } catch (Exception e) {
                    log.warn("获取项目经理信息失败，用户ID: {}", vo.getOwnId(), e);
                }
            }
        }
    }
}
