package com.xinghuo.project.plan.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.plan.entity.ProjectSchemaWorkproductEntity;
import com.xinghuo.project.plan.model.workproduct.ProjectSchemaWorkproductPagination;

import java.util.List;
import java.util.Map;

/**
 * 项目模板交付物计划服务接口
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface ProjectSchemaWorkproductService extends BaseService<ProjectSchemaWorkproductEntity> {

    /**
     * 获取项目模板交付物计划列表
     *
     * @param pagination 分页参数
     * @return 交付物计划列表
     */
    List<ProjectSchemaWorkproductEntity> getList(ProjectSchemaWorkproductPagination pagination);

    /**
     * 根据项目模板ID获取交付物计划列表
     *
     * @param projectTemplateId 项目模板ID
     * @return 交付物计划列表
     */
    List<ProjectSchemaWorkproductEntity> getListByTemplateId(String projectTemplateId);

    /**
     * 根据项目模板阶段ID获取交付物计划列表
     *
     * @param schemaPhaseId 项目模板阶段ID
     * @return 交付物计划列表
     */
    List<ProjectSchemaWorkproductEntity> getListByPhaseId(String schemaPhaseId);

    /**
     * 根据项目模板WBS节点ID获取交付物计划列表
     *
     * @param schemaWbsId 项目模板WBS节点ID
     * @return 交付物计划列表
     */
    List<ProjectSchemaWorkproductEntity> getListByWbsId(String schemaWbsId);

    /**
     * 获取交付物计划详情
     *
     * @param id 交付物计划ID
     * @return 交付物计划信息
     */
    ProjectSchemaWorkproductEntity getInfo(String id);

    /**
     * 创建交付物计划
     *
     * @param entity 交付物计划信息
     * @return 交付物计划ID
     */
    String create(ProjectSchemaWorkproductEntity entity);

    /**
     * 更新交付物计划
     *
     * @param id 交付物计划ID
     * @param entity 交付物计划信息
     */
    void update(String id, ProjectSchemaWorkproductEntity entity);

    /**
     * 删除交付物计划
     *
     * @param id 交付物计划ID
     */
    void delete(String id);

    /**
     * 批量删除交付物计划
     *
     * @param ids 交付物计划ID列表
     */
    void batchDelete(List<String> ids);

    /**
     * 根据项目模板ID删除所有交付物计划
     *
     * @param projectTemplateId 项目模板ID
     */
    void deleteByTemplateId(String projectTemplateId);

    /**
     * 根据项目模板阶段ID删除交付物计划
     *
     * @param schemaPhaseId 项目模板阶段ID
     */
    void deleteByPhaseId(String schemaPhaseId);

    /**
     * 根据项目模板WBS节点ID删除交付物计划
     *
     * @param schemaWbsId 项目模板WBS节点ID
     */
    void deleteByWbsId(String schemaWbsId);

    /**
     * 检查交付物名称在指定模板中是否已存在
     *
     * @param projectTemplateId 项目模板ID
     * @param name 交付物名称
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByName(String projectTemplateId, String name, String excludeId);

    /**
     * 根据标准交付物库ID获取交付物计划
     *
     * @param projectTemplateId 项目模板ID
     * @param libraryWorkproductId 标准交付物库ID
     * @return 交付物计划信息
     */
    ProjectSchemaWorkproductEntity getByLibraryWorkproductId(String projectTemplateId, String libraryWorkproductId);

    /**
     * 获取交付物计划选择列表
     *
     * @param projectTemplateId 项目模板ID
     * @param keyword 关键字
     * @return 交付物计划列表
     */
    List<ProjectSchemaWorkproductEntity> getSelectList(String projectTemplateId, String keyword);

    /**
     * 从标准交付物库复制到项目模板
     *
     * @param projectTemplateId 项目模板ID
     * @param libraryWorkproductIds 标准交付物库ID列表
     * @return 复制的交付物计划数量
     */
    int copyFromLibrary(String projectTemplateId, List<String> libraryWorkproductIds);

    /**
     * 复制交付物计划
     *
     * @param sourceTemplateId 源模板ID
     * @param targetTemplateId 目标模板ID
     * @return 复制的配置数量
     */
    int copyWorkproductConfigs(String sourceTemplateId, String targetTemplateId);

    /**
     * 批量保存交付物计划
     *
     * @param projectTemplateId 项目模板ID
     * @param workproductConfigs 交付物计划列表
     */
    void batchSave(String projectTemplateId, List<ProjectSchemaWorkproductEntity> workproductConfigs);

    /**
     * 更新序号
     *
     * @param id 交付物计划ID
     * @param seqNo 序号
     */
    void updateSeqNo(String id, Integer seqNo);

    /**
     * 批量更新序号
     *
     * @param seqNoMap 序号映射 (ID -> 序号)
     */
    void batchUpdateSeqNo(Map<String, Integer> seqNoMap);

    /**
     * 更新是否必需状态
     *
     * @param id 交付物计划ID
     * @param isRequired 是否必需
     */
    void updateIsRequired(String id, Integer isRequired);

    /**
     * 更新是否需要评审状态
     *
     * @param id 交付物计划ID
     * @param reviewRequired 是否需要评审
     */
    void updateReviewRequired(String id, Integer reviewRequired);

    /**
     * 获取交付物计划统计信息
     *
     * @param projectTemplateId 项目模板ID
     * @return 统计信息
     */
    Map<String, Object> getWorkproductStats(String projectTemplateId);

    /**
     * 获取下一个序号
     *
     * @param projectTemplateId 项目模板ID
     * @return 下一个序号
     */
    Integer getNextSeqNo(String projectTemplateId);

    /**
     * 调整序号（上移/下移）
     *
     * @param id 交付物计划ID
     * @param direction 方向（up/down）
     */
    void adjustSeqNo(String id, String direction);

    /**
     * 获取必需交付物列表
     *
     * @param projectTemplateId 项目模板ID
     * @return 必需交付物列表
     */
    List<ProjectSchemaWorkproductEntity> getRequiredWorkproducts(String projectTemplateId);

    /**
     * 获取需要评审的交付物列表
     *
     * @param projectTemplateId 项目模板ID
     * @return 需要评审的交付物列表
     */
    List<ProjectSchemaWorkproductEntity> getReviewRequiredWorkproducts(String projectTemplateId);
}