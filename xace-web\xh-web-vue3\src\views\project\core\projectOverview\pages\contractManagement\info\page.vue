<template>
  <div class="contract-info-management">
    <div class="page-header">
      <div class="flex items-center">
        <i class="icon-ym icon-ym-contract mr-2 text-lg"></i>
        <span class="text-base font-medium">合同信息</span>
      </div>
      <div class="flex items-center space-x-2">
        <a-button type="primary" @click="handleEdit" v-if="contractInfo && !editMode">
          <template #icon><Icon icon="ant-design:edit-outlined" /></template>
          编辑
        </a-button>
        <a-space v-if="editMode">
          <a-button type="primary" @click="handleSave" :loading="saveLoading"> 保存 </a-button>
          <a-button @click="handleCancel"> 取消 </a-button>
        </a-space>
        <a-button @click="handleRefresh">
          <template #icon><Icon icon="ant-design:reload-outlined" /></template>
          刷新
        </a-button>
      </div>
    </div>

    <div class="page-content">
      <a-spin :spinning="loading">
        <!-- 无合同状态 -->
        <div v-if="!hasContract" class="empty-state">
          <a-empty description="当前项目暂无合同信息">
            <a-button type="primary" @click="handleCreateContract">
              <template #icon><Icon icon="ant-design:plus-outlined" /></template>
              创建合同
            </a-button>
          </a-empty>
        </div>

        <!-- 有合同信息 - 使用统一的 BasicForm 组件 -->
        <div v-else-if="contractInfo" class="contract-detail">
          <a-card class="mb-4">
            <BasicForm @register="registerForm" />
          </a-card>
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script lang="ts" setup>
  // 1. Vue 核心导入
  import { ref, onMounted, onActivated, inject, watch, nextTick } from 'vue';

  // 2. 类型导入
  import type { FormSchema } from '/@/components/Form';
  import type { ContractFormModel } from '/@/api/project/contract';

  // 3. 项目基础组件
  import { Icon } from '/@/components/Icon';
  import { BasicForm, useForm } from '/@/components/Form';

  // 4. Hooks 导入
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useProjectContext } from '/@/hooks/web/useProjectContext';

  // 5. API 导入
  import { updateContract, getContractInfo } from '/@/api/project/contract';
  import { getCustomerList } from '/@/api/project/customer';
  import { getDictionaryDataSelector } from '/@/api/systemData/dictionary';

  // 客户选择API适配函数
  const getCustomerSelectList = async () => {
    try {
      const response = await getCustomerList({ pageSize: 1000 });
      if (response.code === 200 && response.data?.list) {
        return {
          ...response,
          data: {
            ...response.data,
            list: response.data.list.map((item: any) => ({
              id: item.id,
              name: item.name,
              fullName: item.name,
            })),
          },
        };
      }
      return { code: response.code, msg: response.msg, data: { list: [] } };
    } catch (error) {
      return { code: 500, msg: '获取失败', data: { list: [] } };
    }
  };

  // 从父组件注入合同信息
  const contractId = inject('contractId', ref(''));
  const contractInfo = inject('contractInfo', ref(null));
  const hasContract = inject('hasContract', ref(false));

  const { createMessage } = useMessage();

  // 响应式数据
  const loading = ref(false);
  const editMode = ref(false);
  const saveLoading = ref(false);
  const contractStatusOptions = ref<Array<{ label: string; value: string }>>([]);
  const isLoadingContract = ref(false); // 防止重复加载的标志

  // 使用项目上下文Hook
  const { projectId } = useProjectContext({
    onProjectChange: () => {
      editMode.value = false;
    },
  });

  // 加载合同状态数据字典
  const loadContractStatusDict = async () => {
    try {
      const possibleDictTypes = ['contractStatus'];
      let response: any = null;

      for (const dictType of possibleDictTypes) {
        try {
          response = await getDictionaryDataSelector(dictType);
          if (response && response.data && response.data.list && response.data.list.length > 0) {
            break;
          }
        } catch (err) {
          // 继续尝试下一个字典类型
        }
      }

      if (response && response.data && response.data.list && response.data.list.length > 0) {
        // 转换为 Select 组件需要的格式 {label, value}
        contractStatusOptions.value = response.data.list.map((item: any) => ({
          label: item.fullName || item.name || item.text || item.label || '未知状态',
          value: item.enCode || item.id || item.value || item.code || item.key,
        }));

        updateContractStatusOptions();

        if (editMode.value) {
          await nextTick();
        }
      } else {
        // 使用默认合同状态选项
        contractStatusOptions.value = [
          { label: '售前', value: 'ENQUIRY' },
          { label: '已签订', value: 'SIGNED' },
          { label: '执行中', value: 'EXECUTING' },
          { label: '已完成', value: 'COMPLETED' },
          { label: '已终止', value: 'TERMINATED' },
        ];
        updateContractStatusOptions();
      }
    } catch (error) {
      // 使用默认合同状态选项
      contractStatusOptions.value = [
        { label: '售前', value: 'ENQUIRY' },
        { label: '已签订', value: 'SIGNED' },
        { label: '执行中', value: 'EXECUTING' },
        { label: '已完成', value: 'COMPLETED' },
        { label: '已终止', value: 'TERMINATED' },
      ];
      updateContractStatusOptions();
    }
  };

  // 动态更新表单中的合同状态选项
  const updateContractStatusOptions = () => {
    try {
      if (typeof updateStatusEditSchema === 'function') {
        updateStatusEditSchema({
          field: 'contractStatus',
          componentProps: {
            placeholder: '请选择合同状态',
            options: contractStatusOptions.value,
            fieldNames: { label: 'label', value: 'value' },
          },
        });
      }
    } catch (error) {
      // 静默处理错误
    }
  };

  // 统一的表单配置
  const formSchemas: FormSchema[] = [
    // 基础信息分组
    {
      field: 'groupTitleBasic',
      label: '',
      component: 'GroupTitle',
      componentProps: { content: '基础信息' },
      colProps: { span: 24 },
    },
    {
      field: 'name',
      label: '合同名称',
      component: 'Input',
      colProps: { span: 12 },
      required: true,
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请输入合同名称' : '',
      }),
    },
    {
      field: 'cno',
      label: '合同财务编号',
      component: 'Input',
      colProps: { span: 6 },
      required: true,
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请输入合同编号' : '',
      }),
    },
    {
      field: 'signYear',
      label: '合同年度',
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请输入合同年度' : '',
        precision: 0,
        min: 2000,
        max: 2100,
      }),
    },
    {
      field: 'custId',
      label: '客户单位',
      component: 'ApiSelect',
      colProps: { span: 12 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请选择客户单位' : '',
        api: getCustomerSelectList,
        labelField: 'name',
        valueField: 'id',
        showSearch: true,
        allowClear: true,
        resultField: 'data.list',
      }),
    },
    {
      field: 'ownId',
      label: '负责人',
      component: 'UserSelect',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请选择负责人' : '',
      }),
    },
    {
      field: 'deptId',
      label: '所属部门',
      component: 'DepSelect',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请选择部门' : '',
      }),
    },
    {
      field: 'finalUserId',
      label: '最终用户',
      component: 'ApiSelect',
      colProps: { span: 12 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请选择最终用户' : '',
        api: getCustomerSelectList,
        labelField: 'name',
        valueField: 'id',
        showSearch: true,
        allowClear: true,
        resultField: 'data.list',
      }),
    },
    {
      field: 'reportFrequency',
      label: '汇报频率',
      component: 'Select',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请选择汇报频率' : '',
        options: [
          { id: 'weekly', fullName: '每周' },
          { id: 'monthly', fullName: '每月' },
          { id: 'quarterly', fullName: '每季度' },
        ],
        fieldNames: { label: 'fullName', value: 'id' },
      }),
    },

    // 金额信息分组
    {
      field: 'groupTitleAmount',
      label: '',
      component: 'GroupTitle',
      componentProps: { content: '金额信息' },
      colProps: { span: 24 },
    },
    {
      field: 'amount',
      label: '合同金额',
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请输入合同金额' : '',
        precision: 2,
        formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
      }),
    },
    {
      field: 'ysAmount',
      label: '已收金额',
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: true, // 已收金额始终只读
        precision: 2,
        formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
      }),
    },
    {
      field: 'externalAmount',
      label: '外采金额',
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请输入外采金额' : '',
        precision: 2,
        formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
      }),
    },
    {
      field: 'yearYsAmount',
      label: '本年度收款金额',
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: true, // 本年度收款金额始终只读
        precision: 2,
        formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
      }),
    },

    // 日期信息分组
    {
      field: 'groupTitleDate',
      label: '',
      component: 'GroupTitle',
      componentProps: { content: '日期信息' },
      colProps: { span: 24 },
    },
    {
      field: 'signDate',
      label: '签订日期',
      component: 'DatePicker',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        format: 'YYYY-MM-DD',
        placeholder: editMode.value ? '请选择签订日期' : '',
      }),
    },
    {
      field: 'bidDate',
      label: '中标日期',
      component: 'DatePicker',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        format: 'YYYY-MM-DD',
        placeholder: editMode.value ? '请选择中标日期' : '',
      }),
    },
    {
      field: 'commencementDate',
      label: '开工日期',
      component: 'DatePicker',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        format: 'YYYY-MM-DD',
        placeholder: editMode.value ? '请选择开工日期' : '',
      }),
    },
    {
      field: 'initialCheckDate',
      label: '初验日期',
      component: 'DatePicker',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        format: 'YYYY-MM-DD',
        placeholder: editMode.value ? '请选择初验日期' : '',
      }),
    },
    {
      field: 'finalCheckDate',
      label: '终验日期',
      component: 'DatePicker',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        format: 'YYYY-MM-DD',
        placeholder: editMode.value ? '请选择终验日期' : '',
      }),
    },
    {
      field: 'auditDate',
      label: '审计日期',
      component: 'DatePicker',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        format: 'YYYY-MM-DD',
        placeholder: editMode.value ? '请选择审计日期' : '',
      }),
    },

    // 合同周期分组
    {
      field: 'groupTitleCycle',
      label: '',
      component: 'GroupTitle',
      componentProps: { content: '合同周期' },
      colProps: { span: 24 },
    },
    {
      field: 'cstartDate',
      label: '合同开始日期',
      component: 'DatePicker',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        format: 'YYYY-MM-DD',
        placeholder: editMode.value ? '请选择合同开始日期' : '',
      }),
    },
    {
      field: 'cendDate',
      label: '合同结束日期',
      component: 'DatePicker',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        format: 'YYYY-MM-DD',
        placeholder: editMode.value ? '请选择合同结束日期' : '',
      }),
    },
    {
      field: 'mstartDate',
      label: '维保开始日期',
      component: 'DatePicker',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        format: 'YYYY-MM-DD',
        placeholder: editMode.value ? '请选择维保开始日期' : '',
      }),
    },
    {
      field: 'mendDate',
      label: '维保结束日期',
      component: 'DatePicker',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        format: 'YYYY-MM-DD',
        placeholder: editMode.value ? '请选择维保结束日期' : '',
      }),
    },

  // 合同周期表单配置 - 查看模式
  const cycleFormSchemas: FormSchema[] = [
    {
      field: 'cstartDate',
      label: '合同开始日期',
      component: 'DatePicker',
      colProps: { span: 6 },
      componentProps: {
        disabled: true,
        format: 'YYYY-MM-DD',
        placeholder: '合同开始日期',
      },
    },
    {
      field: 'cendDate',
      label: '合同结束日期',
      component: 'DatePicker',
      colProps: { span: 6 },
      componentProps: {
        disabled: true,
        format: 'YYYY-MM-DD',
        placeholder: '合同结束日期',
      },
    },
    {
      field: 'mstartDate',
      label: '维保开始日期',
      component: 'DatePicker',
      colProps: { span: 6 },
      componentProps: {
        disabled: true,
        format: 'YYYY-MM-DD',
        placeholder: '维保开始日期',
      },
    },
    {
      field: 'mendDate',
      label: '维保结束日期',
      component: 'DatePicker',
      colProps: { span: 6 },
      componentProps: {
        disabled: true,
        format: 'YYYY-MM-DD',
        placeholder: '维保结束日期',
      },
    },
  ];

  // 合同周期表单配置 - 编辑模式
  const cycleEditFormSchemas: FormSchema[] = cycleFormSchemas.map(schema => ({
    ...schema,
    componentProps: {
      ...schema.componentProps,
      disabled: false,
    },
  }));

  // 联系人信息表单配置 - 查看模式
  const contactFormSchemas: FormSchema[] = [
   
    {
      field: 'svDeptId',
      label: '监理单位',
      component: 'PopupSelect',
      colProps: { span: 12 },
      componentProps: { disabled: true, placeholder: '监理单位' },
    },
    {
      field: 'svLinkman',
      label: '监理联系人',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: { disabled: true, placeholder: '监理联系人' },
    },
    {
      field: 'svTelephone',
      label: '监理联系电话',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: { disabled: true, placeholder: '监理联系电话' },
    },
    {
      field: 'reviewDeptId',
      label: '测评单位',
      component: 'PopupSelect',
      colProps: { span: 12 },
      componentProps: { disabled: true, placeholder: '测评单位' },
    },
    {
      field: 'reviewLinkman',
      label: '测评联系人',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: { disabled: true, placeholder: '测评联系人' },
    },
    {
      field: 'reviewTelephone',
      label: '测评联系电话',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: { disabled: true, placeholder: '测评联系电话' },
    },
    {
      field: 'dbDeptId',
      label: '等保单位',
      component: 'PopupSelect',
      colProps: { span: 12 },
      componentProps: { disabled: true, placeholder: '等保单位' },
    },
    {
      field: 'dbLinkman',
      label: '等保联系人',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: { disabled: true, placeholder: '等保联系人' },
    },
    {
      field: 'dbTelephone',
      label: '等保联系电话',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: { disabled: true, placeholder: '等保联系电话' },
    },
    {
      field: 'smDeptId',
      label: '商密评测单位',
      component: 'PopupSelect',
      colProps: { span: 12 },
      componentProps: { disabled: true, placeholder: '商密评测单位' },
    },
    {
      field: 'smLinkman',
      label: '商密联系人',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: { disabled: true, placeholder: '商密联系人' },
    },
    {
      field: 'smTelephone',
      label: '商密联系电话',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: { disabled: true, placeholder: '商密联系电话' },
    },
    {
      field: 'jsDeptId',
      label: '结算单位',
      component: 'PopupSelect',
      colProps: { span: 12 },
      componentProps: { disabled: true, placeholder: '结算单位' },
    },
    {
      field: 'jsLinkman',
      label: '结算联系人',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: { disabled: true, placeholder: '结算联系人' },
    },
    {
      field: 'jsTelephone',
      label: '结算联系电话',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: { disabled: true, placeholder: '结算联系电话' },
    },
  ];

  // 联系人信息表单配置 - 编辑模式
  const contactEditFormSchemas: FormSchema[] = contactFormSchemas.map(schema => ({
    ...schema,
    componentProps: {
      ...schema.componentProps,
      disabled: false,
    },
  }));

  // 状态管理表单配置 - 查看模式
  const statusFormSchemas: FormSchema[] = [
    {
      field: 'contractStatus',
      label: '合同状态',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: { disabled: true },
      render: ({ model }) => {
        const status = model.contractStatus;
        // 从数据字典中查找对应的显示文本（根据 enCode 查找 fullName）
        const statusOption = contractStatusOptions.value.find(item => item.value === status);
        return statusOption?.label || '未知';
      },
    },
    {
      field: 'moneyStatus',
      label: '收款状态',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: { disabled: true },
      render: ({ model }) => {
        const status = model.moneyStatus;
        const statusMap: Record<string, string> = {
          '0': '未收款',
          '1': '部分收款',
          '2': '已收款',
        };
        return statusMap[status] || '未知';
      },
    },
    {
      field: 'accdocStatus',
      label: '验收状态',
      component: 'Select',
      colProps: { span: 6 },
      componentProps: {
        disabled: true,
        options: [
          { id: '0', fullName: '未验收' },
          { id: '1', fullName: '初验完成' },
          { id: '2', fullName: '终验完成' },
        ],
      },
    },
    {
      field: 'workStatus',
      label: '工时状态',
      component: 'Select',
      colProps: { span: 6 },
      componentProps: {
        disabled: true,
        options: [
          { id: 0, fullName: '已结束' },
          { id: 1, fullName: '可填写' },
        ],
      },
    },
    {
      field: 'isContinue',
      label: '续签状态',
      component: 'Select',
      colProps: { span: 6 },
      componentProps: {
        disabled: true,
        options: [
          { id: 0, fullName: '正常' },
          { id: 1, fullName: '已续签' },
          { id: 9, fullName: '不续签' },
        ],
      },
    },
    {
      field: 'isArchive',
      label: '归档状态',
      component: 'Select',
      colProps: { span: 6 },
      componentProps: {
        disabled: true,
        options: [
          { id: 0, fullName: '未归档' },
          { id: 1, fullName: '已归档' },
        ],
      },
    },
  ];

  // 状态管理表单配置 - 编辑模式
  const statusEditFormSchemas: FormSchema[] = [
    {
      field: 'contractStatus',
      label: '合同状态',
      component: 'Select',
      colProps: { span: 6 },
      componentProps: {
        placeholder: '请选择合同状态',
        options: contractStatusOptions,
        fieldNames: { label: 'label', value: 'value' },
      },
    },
    {
      field: 'accdocStatus',
      label: '验收状态',
      component: 'Select',
      colProps: { span: 6 },
      componentProps: {
        placeholder: '请选择验收状态',
        options: [
          { id: '0', fullName: '未验收' },
          { id: '1', fullName: '初验完成' },
          { id: '2', fullName: '终验完成' },
        ],
      },
    },
    {
      field: 'isContinue',
      label: '续签状态',
      component: 'Select',
      colProps: { span: 6 },
      componentProps: {
        placeholder: '请选择续签状态',
        options: [
          { id: 0, fullName: '正常' },
          { id: 1, fullName: '已续签' },
          { id: 9, fullName: '不续签' },
        ],
      },
    },
    {
      field: 'isArchive',
      label: '归档状态',
      component: 'Select',
      colProps: { span: 6 },
      componentProps: {
        placeholder: '请选择归档状态',
        options: [
          { id: 0, fullName: '未归档' },
          { id: 1, fullName: '已归档' },
        ],
      },
    },
  ];

  // 毛利分析表单配置 - 查看模式
  const profitFormSchemas: FormSchema[] = [
    {
      field: 'estProbit',
      label: '预估毛利',
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: {
        disabled: true,
        precision: 2,
        formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
        placeholder: '预估毛利',
      },
    },
    {
      field: 'actProbit',
      label: '实际毛利',
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: {
        disabled: true,
        precision: 2,
        formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
        placeholder: '实际毛利',
      },
    },
    {
      field: 'estProbitRatio',
      label: '预估毛利率',
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: {
        disabled: true,
        precision: 2,
        max: 100,
        formatter: (value: number) => `${value}%`,
        placeholder: '预估毛利率',
      },
    },
    {
      field: 'actProbitRatio',
      label: '实际毛利率',
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: {
        disabled: true,
        precision: 2,
        max: 100,
        formatter: (value: number) => `${value}%`,
        placeholder: '实际毛利率',
      },
    },
    {
      field: 'evaExternalAmount',
      label: '采购费用预测',
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: {
        disabled: true,
        precision: 2,
        formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
        placeholder: '采购费用预测',
      },
    },
    {
      field: 'actExternalAmount',
      label: '实际采购金额',
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: {
        disabled: true,
        precision: 2,
        formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
        placeholder: '实际采购金额',
      },
    },
    {
      field: 'evaCostAmount',
      label: '费用预测',
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: {
        disabled: true,
        precision: 2,
        formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
        placeholder: '费用预测',
      },
    },
    {
      field: 'actCostAmount',
      label: '实际费用',
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: {
        disabled: true,
        precision: 2,
        formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
        placeholder: '实际费用',
      },
    },
    {
      field: 'autoManhours',
      label: '累计工时',
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: {
        disabled: true,
        precision: 1,
        formatter: (value: number) => `${value}小时`,
        placeholder: '累计工时',
      },
    },
    {
      field: 'autoRatioLevel',
      label: '毛利率区间',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: { disabled: true, placeholder: '毛利率区间' },
    },
  ];

  // 毛利分析表单配置 - 编辑模式
  const profitEditFormSchemas: FormSchema[] = profitFormSchemas.map(schema => ({
    ...schema,
    componentProps: {
      ...schema.componentProps,
      disabled:
        schema.field === 'actProbit' ||
        schema.field === 'actExternalAmount' ||
        schema.field === 'actCostAmount' ||
        schema.field === 'autoManhours' ||
        schema.field === 'autoRatioLevel'
          ? true
          : false, // 实际数据和自动计算字段保持只读
    },
  }));

  // 备注信息表单配置 - 查看模式
  const noteFormSchemas: FormSchema[] = [
    {
      field: 'note',
      label: '备注',
      component: 'Textarea',
      colProps: { span: 24 },
      componentProps: {
        disabled: true,
        rows: 4,
        placeholder: '备注信息',
      },
    },
    {
      field: 'continueDesc',
      label: '续签说明',
      component: 'Textarea',
      colProps: { span: 24 },
      componentProps: {
        disabled: true,
        rows: 3,
        placeholder: '续签说明',
      },
    },
    {
      field: 'accdocPath',
      label: '验收文档路径',
      component: 'Input',
      colProps: { span: 24 },
      componentProps: {
        disabled: true,
        placeholder: '验收文档路径',
      },
    },
  ];

  // 备注信息表单配置 - 编辑模式
  const noteEditFormSchemas: FormSchema[] = noteFormSchemas.map(schema => ({
    ...schema,
    componentProps: {
      ...schema.componentProps,
      disabled: false,
    },
  }));

  // 创建表单实例
  const [registerBasicForm, { setFieldsValue: setBasicFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: basicFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  const [registerBasicEditForm, { validate: validateBasic, setFieldsValue: setBasicEditFieldsValue, updateSchema: updateBasicEditSchema }] = useForm({
    labelWidth: 120,
    schemas: basicEditFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  const [registerAmountForm, { setFieldsValue: setAmountFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: amountFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  // 金额信息表单配置 - 编辑模式
  const amountEditFormSchemas: FormSchema[] = amountFormSchemas.map(schema => ({
    ...schema,
    componentProps: {
      ...schema.componentProps,
      disabled: false,
    },
  }));

  const [registerAmountEditForm, { validate: validateAmount, setFieldsValue: setAmountEditFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: amountEditFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  // 创建其他表单实例
  const [registerDateForm, { setFieldsValue: setDateFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: dateFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  const [registerDateEditForm, { validate: validateDate, setFieldsValue: setDateEditFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: dateEditFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  const [registerCycleForm, { setFieldsValue: setCycleFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: cycleFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  const [registerCycleEditForm, { validate: validateCycle, setFieldsValue: setCycleEditFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: cycleEditFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  const [registerContactForm, { setFieldsValue: setContactFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: contactFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  const [registerContactEditForm, { validate: validateContact, setFieldsValue: setContactEditFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: contactEditFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  const [registerStatusForm, { setFieldsValue: setStatusFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: statusFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  const [registerStatusEditForm, { validate: validateStatus, setFieldsValue: setStatusEditFieldsValue, updateSchema: updateStatusEditSchema }] = useForm({
    labelWidth: 120,
    schemas: statusEditFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  const [registerProfitForm, { setFieldsValue: setProfitFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: profitFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  const [registerProfitEditForm, { validate: validateProfit, setFieldsValue: setProfitEditFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: profitEditFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  const [registerNoteForm, { setFieldsValue: setNoteFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: noteFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  const [registerNoteEditForm, { validate: validateNote, setFieldsValue: setNoteEditFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: noteEditFormSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  // 安全设置表单数据的辅助函数
  const safeSetFieldsValue = async (setterFn: Function, data: any, formName: string) => {
    try {
      // 检查表单名是否为编辑表单且当前不在编辑模式
      if (formName.includes('编辑') && !editMode.value) {
        return;
      }

      // 等待表单就绪
      let retries = 0;
      while (retries < 10) {
        try {
          await setterFn(data);
          return;
        } catch (error) {
          retries++;
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      }
    } catch (error) {
      // 静默处理错误
    }
  };

  // 提取合同数据的公共方法
  const extractContractFormData = (contract: any) => {
    return {
      basic: {
        name: contract.name || '',
        cno: contract.cno || '',
        custId: contract.custId || '',
        finalUserId: contract.finalUserId || '',
        ownId: contract.ownId || '',
        deptId: contract.deptId || '',
        reportFrequency: contract.reportFrequency || '',
        signYear: contract.signYear || null,
      },
      amount: {
        amount: contract.amount || 0,
        ysAmount: contract.ysAmount || 0,
        yearYsAmount: contract.yearYsAmount || 0,
        externalAmount: contract.externalAmount || 0,
        moneyStatus: contract.moneyStatus || '',
      },
      date: {
        signDate: contract.signDate || undefined,
        bidDate: contract.bidDate || undefined,
        commencementDate: contract.commencementDate || undefined,
        initialCheckDate: contract.initialCheckDate || undefined,
        finalCheckDate: contract.finalCheckDate || undefined,
        auditDate: contract.auditDate || undefined,
      },
      cycle: {
        cstartDate: contract.cstartDate || undefined,
        cendDate: contract.cendDate || undefined,
        mstartDate: contract.mstartDate || undefined,
        mendDate: contract.mendDate || undefined,
      },
      contact: {
        linkman: contract.linkman || '',
        linkTelephone: contract.linkTelephone || '',
        svDeptId: contract.svDeptId || '',
        svLinkman: contract.svLinkman || '',
        svTelephone: contract.svTelephone || '',
        reviewDeptId: contract.reviewDeptId || '',
        reviewLinkman: contract.reviewLinkman || '',
        reviewTelephone: contract.reviewTelephone || '',
        dbDeptId: contract.dbDeptId || '',
        dbLinkman: contract.dbLinkman || '',
        dbTelephone: contract.dbTelephone || '',
        smDeptId: contract.smDeptId || '',
        smLinkman: contract.smLinkman || '',
        smTelephone: contract.smTelephone || '',
        jsDeptId: contract.jsDeptId || '',
        jsLinkman: contract.jsLinkman || '',
        jsTelephone: contract.jsTelephone || '',
      },
      status: {
        contractStatus: contract.contractStatus || '',
        moneyStatus: contract.moneyStatus || '',
        accdocStatus: contract.accdocStatus || '',
        isContinue: contract.isContinue || 0,
        isArchive: contract.isArchive || 0,
        workStatus: contract.workStatus || 0,
      },
      profit: {
        estProbit: contract.estProbit || 0,
        actProbit: contract.actProbit || 0,
        estProbitRatio: contract.estProbitRatio || 0,
        actProbitRatio: contract.actProbitRatio || 0,
        evaExternalAmount: contract.evaExternalAmount || 0,
        actExternalAmount: contract.actExternalAmount || 0,
        evaCostAmount: contract.evaCostAmount || 0,
        actCostAmount: contract.actCostAmount || 0,
        autoManhours: contract.autoManhours || 0,
        autoRatioLevel: contract.autoRatioLevel || '',
      },
      note: {
        note: contract.note || '',
        continueDesc: contract.continueDesc || '',
        accdocPath: contract.accdocPath || '',
      },
    };
  };

  // 统一设置表单数据的方法
  const setAllFormData = async (contract: any, isEditMode = false) => {
    if (!contract) return;

    await new Promise(resolve => setTimeout(resolve, 300));
    const formData = extractContractFormData(contract);

    // 设置查看表单数据
    if (!isEditMode) {
      await Promise.all([
        safeSetFieldsValue(setBasicFieldsValue, formData.basic, '基础信息查看'),
        safeSetFieldsValue(setAmountFieldsValue, formData.amount, '金额信息查看'),
        safeSetFieldsValue(setDateFieldsValue, formData.date, '日期信息查看'),
        safeSetFieldsValue(setCycleFieldsValue, formData.cycle, '合同周期查看'),
        safeSetFieldsValue(setContactFieldsValue, formData.contact, '联系人信息查看'),
        safeSetFieldsValue(setStatusFieldsValue, formData.status, '状态管理查看'),
        safeSetFieldsValue(setProfitFieldsValue, formData.profit, '毛利分析查看'),
        safeSetFieldsValue(setNoteFieldsValue, formData.note, '备注信息查看'),
      ]);
    }

    // 设置编辑表单数据
    if (isEditMode) {
      await Promise.all([
        safeSetFieldsValue(setBasicEditFieldsValue, formData.basic, '基础信息编辑'),
        safeSetFieldsValue(setAmountEditFieldsValue, formData.amount, '金额信息编辑'),
        safeSetFieldsValue(setDateEditFieldsValue, formData.date, '日期信息编辑'),
        safeSetFieldsValue(setCycleEditFieldsValue, formData.cycle, '合同周期编辑'),
        safeSetFieldsValue(setContactEditFieldsValue, formData.contact, '联系人信息编辑'),
        safeSetFieldsValue(setStatusEditFieldsValue, formData.status, '状态管理编辑'),
        safeSetFieldsValue(setProfitEditFieldsValue, formData.profit, '毛利分析编辑'),
        safeSetFieldsValue(setNoteEditFieldsValue, formData.note, '备注信息编辑'),
      ]);
    }
  };

  // 加载合同信息
  const loadContractInfo = async (showSuccessMessage = true) => {
    if (!contractId.value) {
      console.log('⚠️ 合同ID为空，无法加载合同信息');
      return;
    }

    // 防止重复加载
    if (isLoadingContract.value) {
      console.log('⚠️ [合同信息] 正在加载中，跳过重复请求');
      return;
    }

    isLoadingContract.value = true;
    loading.value = true;
    try {
      console.log('🔄 [合同信息] 开始从后台重新加载合同数据，合同ID:', contractId.value);

      // 从后台重新获取最新的合同信息
      const response = await getContractInfo(contractId.value);

      if ((response as any).code === 200 || (response as any).data) {
        const latestContractInfo = (response as any).data || response;
        console.log('✅ [合同信息] 从后台获取最新合同数据成功:', latestContractInfo);

        // 更新注入的合同信息（如果可能的话）
        if (contractInfo.value && typeof contractInfo.value === 'object') {
          Object.assign(contractInfo.value, latestContractInfo);
        }

        // 将最新数据设置到表单中
        await setAllFormData(latestContractInfo);

        console.log('✅ [合同信息] 合同信息刷新完成');
        if (showSuccessMessage) {
          createMessage.success('合同信息已刷新');
        }
      } else {
        throw new Error((response as any).msg || '获取合同信息失败');
      }
    } catch (error) {
      console.error('❌ [合同信息] 加载合同信息失败:', error);
      createMessage.error('加载合同信息失败');
    } finally {
      loading.value = false;
      isLoadingContract.value = false;
    }
  };

  // 编辑合同
  const handleEdit = async () => {
    // 确保字典数据已加载
    if (!contractStatusOptions.value || contractStatusOptions.value.length === 0) {
      await loadContractStatusDict();
    }

    editMode.value = true;

    // 切换到编辑模式后，为编辑表单设置数据
    if (contractInfo.value) {
      await nextTick();
      await setAllFormData(contractInfo.value, true);
    }
  };

  // 保存合同信息
  const handleSave = async () => {
    try {
      saveLoading.value = true;

      // 验证所有表单
      const basicData = await validateBasic();
      const amountData = await validateAmount();
      const dateData = await validateDate();
      const cycleData = await validateCycle();
      const contactData = await validateContact();
      const statusData = await validateStatus();
      const profitData = await validateProfit();
      const noteData = await validateNote();

      // 构建提交数据
      const submitData: ContractFormModel = {
        projBaseId: projectId.value,

        // 基础信息
        name: basicData.name,
        cno: basicData.cno,
        custId: basicData.custId || '',
        finalUserId: basicData.finalUserId || '',
        ownId: basicData.ownId || '',
        reportFrequency: basicData.reportFrequency || '',
        deptId: basicData.deptId || '',

        // 金额信息
        amount: amountData.amount || 0,
        ysAmount: amountData.ysAmount || (contractInfo.value as any)?.ysAmount || 0,
        yearYsAmount: amountData.yearYsAmount || (contractInfo.value as any)?.yearYsAmount || 0,
        externalAmount: amountData.externalAmount || undefined,

        // 状态信息
        moneyStatus: (contractInfo.value as any)?.moneyStatus || '',
        contractStatus: statusData.contractStatus || '',
        typeStatus: (contractInfo.value as any)?.typeStatus || '',

        // 其他信息
        note: noteData.note || '',
        signYear: dateData.signDate ? new Date(dateData.signDate).getFullYear() : undefined,
        linkman: contactData.linkman || '',
        linkTelephone: contactData.linkTelephone || '',

        // 日期信息
        signDate: dateData.signDate || undefined,
        bidDate: dateData.bidDate || undefined,
        commencementDate: dateData.commencementDate || undefined,
        initialCheckDate: dateData.initialCheckDate || undefined,
        finalCheckDate: dateData.finalCheckDate || undefined,
        auditDate: dateData.auditDate || undefined,

        // 合同周期
        cstartDate: cycleData.cstartDate || undefined,
        cendDate: cycleData.cendDate || undefined,
        mstartDate: cycleData.mstartDate || undefined,
        mendDate: cycleData.mendDate || undefined,

        // 毛利分析
        evaExternalAmount: profitData.evaExternalAmount || undefined,
        evaCostAmount: profitData.evaCostAmount || undefined,
        actExternalAmount: profitData.actExternalAmount || undefined,
        actCostAmount: profitData.actCostAmount || undefined,
        unsignExternalAmount: profitData.unsignExternalAmount || undefined,
        estProbit: profitData.estProbit || undefined,
        actProbit: profitData.actProbit || undefined,
        estProbitRatio: profitData.estProbitRatio || undefined,
        actProbitRatio: profitData.actProbitRatio || undefined,

        // 分部金额字段（从现有合同信息中获取）
        yfYbAmount: (contractInfo.value as any)?.yfYbAmount || undefined,
        yfEbAmount: (contractInfo.value as any)?.yfEbAmount || undefined,
        yfJfAmount: (contractInfo.value as any)?.yfJfAmount || undefined,
        yfOtherAmount: (contractInfo.value as any)?.yfOtherAmount || undefined,
        outYbAmount: (contractInfo.value as any)?.outYbAmount || undefined,
        outEbAmount: (contractInfo.value as any)?.outEbAmount || undefined,
        outJfAmount: (contractInfo.value as any)?.outJfAmount || undefined,
        outOtherAmount: (contractInfo.value as any)?.outOtherAmount || undefined,
        unsignOutYbAmount: (contractInfo.value as any)?.unsignOutYbAmount || undefined,
        unsignOutEbAmount: (contractInfo.value as any)?.unsignOutEbAmount || undefined,
        unsignOutJfAmount: (contractInfo.value as any)?.unsignOutJfAmount || undefined,
        unsignOutOtherAmount: (contractInfo.value as any)?.unsignOutOtherAmount || undefined,
        outYfYbAmount: (contractInfo.value as any)?.outYfYbAmount || undefined,
        outYfEbAmount: (contractInfo.value as any)?.outYfEbAmount || undefined,
        outYfJfAmount: (contractInfo.value as any)?.outYfJfAmount || undefined,
      };

      // 调用合同更新API
      await updateContract(contractId.value, submitData);

      createMessage.success('合同信息更新成功');
      editMode.value = false;

      // 重新加载数据，不显示额外的成功消息
      await loadContractInfo(false);
    } catch (error) {
      createMessage.error('保存合同信息失败');
    } finally {
      saveLoading.value = false;
    }
  };

  // 取消编辑
  const handleCancel = () => {
    editMode.value = false;
    loadContractInfo(false); // 重新加载数据，不显示成功消息
  };

  // 创建合同
  const handleCreateContract = () => {
    createMessage.info('创建合同功能开发中...');
  };

  // 刷新
  const handleRefresh = () => {
    loadContractInfo(true); // 手动刷新，显示成功消息
  };

  // 监听合同信息变化
  watch(
    contractInfo,
    newContractInfo => {
      if (newContractInfo) {
        // 延迟加载，确保表单组件已准备就绪
        setTimeout(() => {
          loadContractInfo(false); // 自动加载，不显示成功消息
        }, 300);
      } else {
        // 合同信息清空时，退出编辑模式
        editMode.value = false;
      }
    },
    { immediate: false, deep: true },
  );

  onMounted(async () => {
    // 首先加载数据字典
    await loadContractStatusDict();
    await nextTick();

    // 延迟加载，确保所有表单组件都已渲染
    setTimeout(() => {
      if (contractInfo.value) {
        loadContractInfo(false); // 初始加载，不显示成功消息
      }
    }, 500);
  });

  onActivated(async () => {
    // 如果数据字典还没加载，先加载
    if (!contractStatusOptions.value || contractStatusOptions.value.length === 0) {
      await loadContractStatusDict();
      await nextTick();
    }

    // 延迟加载，确保所有表单组件都已渲染
    setTimeout(() => {
      if (contractInfo.value) {
        loadContractInfo(false); // 激活时加载，不显示成功消息
      }
    }, 500);
  });
</script>

<style lang="less" scoped>
  .contract-info-management {
    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 16px;
      background: #fff;
      border-radius: 6px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    }

    .page-content {
      .contract-detail {
        .ant-card {
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
        }
      }

      .empty-state {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 400px;
        background: #fff;
        border-radius: 6px;
      }
    }
  }
</style>
