<template>
  <div class="contract-info-management">
    <div class="page-header">
      <div class="flex items-center">
        <i class="icon-ym icon-ym-contract mr-2 text-lg"></i>
        <span class="text-base font-medium">合同信息</span>
      </div>
      <div class="flex items-center space-x-2">
        <a-button type="primary" @click="handleEdit" v-if="contractInfo && !editMode">
          <template #icon><Icon icon="ant-design:edit-outlined" /></template>
          编辑
        </a-button>
        <a-space v-if="editMode">
          <a-button type="primary" @click="handleSave" :loading="saveLoading"> 保存 </a-button>
          <a-button @click="handleCancel"> 取消 </a-button>
        </a-space>
        <a-button @click="handleRefresh">
          <template #icon><Icon icon="ant-design:reload-outlined" /></template>
          刷新
        </a-button>
      </div>
    </div>

    <div class="page-content">
      <a-spin :spinning="loading">
        <!-- 无合同状态 -->
        <div v-if="!hasContract" class="empty-state">
          <a-empty description="当前项目暂无合同信息">
            <a-button type="primary" @click="handleCreateContract">
              <template #icon><Icon icon="ant-design:plus-outlined" /></template>
              创建合同
            </a-button>
          </a-empty>
        </div>

        <!-- 有合同信息 - 使用统一的 BasicForm 组件 -->
        <div v-else-if="contractInfo" class="contract-detail">
          <a-card class="mb-4">
            <BasicForm @register="registerForm" />
          </a-card>
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script lang="ts" setup>
  // 1. Vue 核心导入
  import { ref, onMounted, onActivated, inject, watch, nextTick } from 'vue';

  // 2. 类型导入
  import type { FormSchema } from '/@/components/Form';
  import type { ContractFormModel } from '/@/api/project/contract';

  // 3. 项目基础组件
  import { Icon } from '/@/components/Icon';
  import { BasicForm, useForm } from '/@/components/Form';

  // 4. Hooks 导入
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useProjectContext } from '/@/hooks/web/useProjectContext';

  // 5. API 导入
  import { updateContract, getContractInfo } from '/@/api/project/contract';
  import { getCustomerList } from '/@/api/project/customer';
  import { getDictionaryDataSelector } from '/@/api/systemData/dictionary';

  // 客户选择API适配函数
  const getCustomerSelectList = async () => {
    try {
      const response = await getCustomerList({ pageSize: 1000 });
      if (response.code === 200 && response.data?.list) {
        return {
          ...response,
          data: {
            ...response.data,
            list: response.data.list.map((item: any) => ({
              id: item.id,
              name: item.name,
              fullName: item.name,
            })),
          },
        };
      }
      return { code: response.code, msg: response.msg, data: { list: [] } };
    } catch (error) {
      return { code: 500, msg: '获取失败', data: { list: [] } };
    }
  };

  // 从父组件注入合同信息
  const contractId = inject('contractId', ref(''));
  const contractInfo = inject('contractInfo', ref(null));
  const hasContract = inject('hasContract', ref(false));

  const { createMessage } = useMessage();

  // 响应式数据
  const loading = ref(false);
  const editMode = ref(false);
  const saveLoading = ref(false);
  const contractStatusOptions = ref<Array<{ label: string; value: string }>>([]);
  const isLoadingContract = ref(false); // 防止重复加载的标志

  // 使用项目上下文Hook
  const { projectId } = useProjectContext({
    onProjectChange: () => {
      editMode.value = false;
    },
  });

  // 加载合同状态数据字典
  const loadContractStatusDict = async () => {
    try {
      const possibleDictTypes = ['contractStatus'];
      let response: any = null;

      for (const dictType of possibleDictTypes) {
        try {
          response = await getDictionaryDataSelector(dictType);
          if (response && response.data && response.data.list && response.data.list.length > 0) {
            break;
          }
        } catch (err) {
          // 继续尝试下一个字典类型
        }
      }

      if (response && response.data && response.data.list && response.data.list.length > 0) {
        // 转换为 Select 组件需要的格式 {label, value}
        contractStatusOptions.value = response.data.list.map((item: any) => ({
          label: item.fullName || item.name || item.text || item.label || '未知状态',
          value: item.enCode || item.id || item.value || item.code || item.key,
        }));

        updateContractStatusOptions();

        if (editMode.value) {
          await nextTick();
        }
      } else {
        // 使用默认合同状态选项
        contractStatusOptions.value = [
          { label: '售前', value: 'ENQUIRY' },
          { label: '已签订', value: 'SIGNED' },
          { label: '执行中', value: 'EXECUTING' },
          { label: '已完成', value: 'COMPLETED' },
          { label: '已终止', value: 'TERMINATED' },
        ];
        updateContractStatusOptions();
      }
    } catch (error) {
      // 使用默认合同状态选项
      contractStatusOptions.value = [
        { label: '售前', value: 'ENQUIRY' },
        { label: '已签订', value: 'SIGNED' },
        { label: '执行中', value: 'EXECUTING' },
        { label: '已完成', value: 'COMPLETED' },
        { label: '已终止', value: 'TERMINATED' },
      ];
      updateContractStatusOptions();
    }
  };

  // 动态更新表单中的状态字段选项
  const updateStatusFields = () => {
    console.log('🔄 updateStatusFields called, editMode:', editMode.value, 'contractStatusOptions:', contractStatusOptions.value.length);
    try {
      if (typeof updateSchema === 'function') {
        // 更新合同状态字段
        const contractStatusConfig: any = {
          field: 'contractStatus',
          componentProps: {
            disabled: !editMode.value,
            placeholder: editMode.value ? '请选择合同状态' : '',
            options: contractStatusOptions.value,
            fieldNames: { label: 'label', value: 'value' },
          },
        };

        // 在查看模式下添加render函数
        if (!editMode.value) {
          contractStatusConfig.render = ({ model }) => {
            const status = model.contractStatus;
            const statusOption = contractStatusOptions.value.find(item => item.value === status);
            return statusOption?.label || '未知';
          };
        }

        updateSchema(contractStatusConfig);

        // 更新收款状态字段
        const moneyStatusConfig: any = {
          field: 'moneyStatus',
          componentProps: {
            disabled: !editMode.value,
            placeholder: editMode.value ? '请选择收款状态' : '',
            options: [
              { id: '0', fullName: '未收款' },
              { id: '1', fullName: '部分收款' },
              { id: '2', fullName: '已收款' },
            ],
            fieldNames: { label: 'fullName', value: 'id' },
          },
        };

        // 在查看模式下添加render函数
        if (!editMode.value) {
          moneyStatusConfig.render = ({ model }) => {
            const status = model.moneyStatus;
            const statusMap: Record<string, string> = {
              '0': '未收款',
              '1': '部分收款',
              '2': '已收款',
            };
            return statusMap[status] || '未知';
          };
        }

        updateSchema(moneyStatusConfig);
      }
    } catch (error) {
      // 静默处理错误
    }
  };

  // 兼容旧的函数名
  const updateContractStatusOptions = updateStatusFields;

  // 统一的表单配置
  const formSchemas: FormSchema[] = [
    // 基础信息分组
    {
      field: 'groupTitleBasic',
      label: '',
      component: 'GroupTitle',
      componentProps: { content: '基础信息' },
      colProps: { span: 24 },
    },
    {
      field: 'name',
      label: '合同名称',
      component: 'Input',
      colProps: { span: 12 },
      required: true,
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请输入合同名称' : '',
      }),
    },
    {
      field: 'cno',
      label: '合同财务编号',
      component: 'Input',
      colProps: { span: 6 },
      required: true,
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请输入合同编号' : '',
      }),
    },
    {
      field: 'signYear',
      label: '合同年度',
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请输入合同年度' : '',
        precision: 0,
        min: 2000,
        max: 2100,
      }),
    },
    {
      field: 'custId',
      label: '客户单位',
      component: 'ApiSelect',
      colProps: { span: 12 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请选择客户单位' : '',
        api: getCustomerSelectList,
        labelField: 'name',
        valueField: 'id',
        showSearch: true,
        allowClear: true,
        resultField: 'data.list',
      }),
    },
    {
      field: 'ownId',
      label: '负责人',
      component: 'UserSelect',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请选择负责人' : '',
      }),
    },
    {
      field: 'deptId',
      label: '所属部门',
      component: 'DepSelect',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请选择部门' : '',
      }),
    },
    {
      field: 'finalUserId',
      label: '最终用户',
      component: 'ApiSelect',
      colProps: { span: 12 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请选择最终用户' : '',
        api: getCustomerSelectList,
        labelField: 'name',
        valueField: 'id',
        showSearch: true,
        allowClear: true,
        resultField: 'data.list',
      }),
    },
    {
      field: 'reportFrequency',
      label: '汇报频率',
      component: 'Select',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请选择汇报频率' : '',
        options: [
          { id: 'weekly', fullName: '每周' },
          { id: 'monthly', fullName: '每月' },
          { id: 'quarterly', fullName: '每季度' },
        ],
        fieldNames: { label: 'fullName', value: 'id' },
      }),
    },

    // 金额信息分组
    {
      field: 'groupTitleAmount',
      label: '',
      component: 'GroupTitle',
      componentProps: { content: '金额信息' },
      colProps: { span: 24 },
    },
    {
      field: 'amount',
      label: '合同金额',
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请输入合同金额' : '',
        precision: 2,
        formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
      }),
    },
    {
      field: 'ysAmount',
      label: '已收金额',
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: true, // 已收金额始终只读
        precision: 2,
        formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
      }),
    },
    {
      field: 'externalAmount',
      label: '外采金额',
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请输入外采金额' : '',
        precision: 2,
        formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
      }),
    },
    {
      field: 'yearYsAmount',
      label: '本年度收款金额',
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: true, // 本年度收款金额始终只读
        precision: 2,
        formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
      }),
    },

    // 日期信息分组
    {
      field: 'groupTitleDate',
      label: '',
      component: 'GroupTitle',
      componentProps: { content: '日期信息' },
      colProps: { span: 24 },
    },
    {
      field: 'signDate',
      label: '签订日期',
      component: 'DatePicker',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        format: 'YYYY-MM-DD',
        placeholder: editMode.value ? '请选择签订日期' : '',
      }),
    },
    {
      field: 'bidDate',
      label: '中标日期',
      component: 'DatePicker',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        format: 'YYYY-MM-DD',
        placeholder: editMode.value ? '请选择中标日期' : '',
      }),
    },
    {
      field: 'commencementDate',
      label: '开工日期',
      component: 'DatePicker',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        format: 'YYYY-MM-DD',
        placeholder: editMode.value ? '请选择开工日期' : '',
      }),
    },
    {
      field: 'initialCheckDate',
      label: '初验日期',
      component: 'DatePicker',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        format: 'YYYY-MM-DD',
        placeholder: editMode.value ? '请选择初验日期' : '',
      }),
    },
    {
      field: 'finalCheckDate',
      label: '终验日期',
      component: 'DatePicker',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        format: 'YYYY-MM-DD',
        placeholder: editMode.value ? '请选择终验日期' : '',
      }),
    },
    {
      field: 'auditDate',
      label: '审计日期',
      component: 'DatePicker',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        format: 'YYYY-MM-DD',
        placeholder: editMode.value ? '请选择审计日期' : '',
      }),
    },

    // 合同周期分组
    {
      field: 'groupTitleCycle',
      label: '',
      component: 'GroupTitle',
      componentProps: { content: '合同周期' },
      colProps: { span: 24 },
    },
    {
      field: 'cstartDate',
      label: '合同开始日期',
      component: 'DatePicker',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        format: 'YYYY-MM-DD',
        placeholder: editMode.value ? '请选择合同开始日期' : '',
      }),
    },
    {
      field: 'cendDate',
      label: '合同结束日期',
      component: 'DatePicker',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        format: 'YYYY-MM-DD',
        placeholder: editMode.value ? '请选择合同结束日期' : '',
      }),
    },
    {
      field: 'mstartDate',
      label: '维保开始日期',
      component: 'DatePicker',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        format: 'YYYY-MM-DD',
        placeholder: editMode.value ? '请选择维保开始日期' : '',
      }),
    },
    {
      field: 'mendDate',
      label: '维保结束日期',
      component: 'DatePicker',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        format: 'YYYY-MM-DD',
        placeholder: editMode.value ? '请选择维保结束日期' : '',
      }),
    },

    // 联系人信息分组
    {
      field: 'groupTitleContact',
      label: '',
      component: 'GroupTitle',
      componentProps: { content: '联系人信息' },
      colProps: { span: 24 },
    },
    {
      field: 'linkman',
      label: '合同联系人',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请输入合同联系人' : '',
      }),
    },
    {
      field: 'linkTelephone',
      label: '联系电话',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请输入联系电话' : '',
      }),
    },
    {
      field: 'svDeptId',
      label: '监理单位',
      component: 'PopupSelect',
      colProps: { span: 12 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请选择监理单位' : '',
      }),
    },
    {
      field: 'svLinkman',
      label: '监理联系人',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请输入监理联系人' : '',
      }),
    },
    {
      field: 'svTelephone',
      label: '监理联系电话',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请输入监理联系电话' : '',
      }),
    },
    {
      field: 'reviewDeptId',
      label: '测评单位',
      component: 'PopupSelect',
      colProps: { span: 12 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请选择测评单位' : '',
      }),
    },
    {
      field: 'reviewLinkman',
      label: '测评联系人',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请输入测评联系人' : '',
      }),
    },
    {
      field: 'reviewTelephone',
      label: '测评联系电话',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请输入测评联系电话' : '',
      }),
    },
    {
      field: 'dbDeptId',
      label: '等保单位',
      component: 'PopupSelect',
      colProps: { span: 12 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请选择等保单位' : '',
      }),
    },
    {
      field: 'dbLinkman',
      label: '等保联系人',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请输入等保联系人' : '',
      }),
    },
    {
      field: 'dbTelephone',
      label: '等保联系电话',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请输入等保联系电话' : '',
      }),
    },
    {
      field: 'smDeptId',
      label: '商密评测单位',
      component: 'PopupSelect',
      colProps: { span: 12 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请选择商密评测单位' : '',
      }),
    },
    {
      field: 'smLinkman',
      label: '商密联系人',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请输入商密联系人' : '',
      }),
    },
    {
      field: 'smTelephone',
      label: '商密联系电话',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请输入商密联系电话' : '',
      }),
    },
    {
      field: 'jsDeptId',
      label: '结算单位',
      component: 'PopupSelect',
      colProps: { span: 12 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请选择结算单位' : '',
      }),
    },
    {
      field: 'jsLinkman',
      label: '结算联系人',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请输入结算联系人' : '',
      }),
    },
    {
      field: 'jsTelephone',
      label: '结算联系电话',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请输入结算联系电话' : '',
      }),
    },

    // 状态管理分组
    {
      field: 'groupTitleStatus',
      label: '',
      component: 'GroupTitle',
      componentProps: { content: '状态管理' },
      colProps: { span: 24 },
    },
    {
      field: 'contractStatus',
      label: '合同状态',
      component: 'Select',
      colProps: { span: 6 },
      componentProps: {
        disabled: true,
        placeholder: '请选择合同状态',
        options: [],
        fieldNames: { label: 'label', value: 'value' },
      },
    },
    {
      field: 'moneyStatus',
      label: '收款状态',
      component: 'Select',
      colProps: { span: 6 },
      componentProps: {
        disabled: true,
        placeholder: '请选择收款状态',
        options: [
          { id: '0', fullName: '未收款' },
          { id: '1', fullName: '部分收款' },
          { id: '2', fullName: '已收款' },
        ],
        fieldNames: { label: 'fullName', value: 'id' },
      },
    },
    {
      field: 'accdocStatus',
      label: '验收状态',
      component: 'Select',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请选择验收状态' : '',
        options: [
          { id: '0', fullName: '未验收' },
          { id: '1', fullName: '初验完成' },
          { id: '2', fullName: '终验完成' },
        ],
        fieldNames: { label: 'fullName', value: 'id' },
      }),
    },
    {
      field: 'workStatus',
      label: '工时状态',
      component: 'Select',
      colProps: { span: 6 },
      componentProps: { disabled: true },
      render: ({ model }) => {
        const status = model.workStatus;
        const statusMap: Record<string, string> = {
          '0': '已结束',
          '1': '可填写',
        };
        return statusMap[status] || '未知';
      },
    },
    {
      field: 'isContinue',
      label: '续签状态',
      component: 'Select',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请选择续签状态' : '',
        options: [
          { id: 0, fullName: '正常' },
          { id: 1, fullName: '已续签' },
          { id: 9, fullName: '不续签' },
        ],
        fieldNames: { label: 'fullName', value: 'id' },
      }),
    },
    {
      field: 'isArchive',
      label: '归档状态',
      component: 'Select',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请选择归档状态' : '',
        options: [
          { id: 0, fullName: '未归档' },
          { id: 1, fullName: '已归档' },
        ],
        fieldNames: { label: 'fullName', value: 'id' },
      }),
    },

    // 毛利分析分组
    {
      field: 'groupTitleProfit',
      label: '',
      component: 'GroupTitle',
      componentProps: { content: '毛利分析' },
      colProps: { span: 24 },
    },
    {
      field: 'estProbit',
      label: '预估毛利',
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请输入预估毛利' : '',
        precision: 2,
        formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
      }),
    },
    {
      field: 'actProbit',
      label: '实际毛利',
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: { disabled: true, precision: 2, formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') },
    },
    {
      field: 'estProbitRatio',
      label: '预估毛利率',
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请输入预估毛利率' : '',
        precision: 2,
        max: 100,
        formatter: (value: number) => `${value}%`,
      }),
    },
    {
      field: 'actProbitRatio',
      label: '实际毛利率',
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: { disabled: true, precision: 2, max: 100, formatter: (value: number) => `${value}%` },
    },
    {
      field: 'evaExternalAmount',
      label: '采购费用预测',
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请输入采购费用预测' : '',
        precision: 2,
        formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
      }),
    },
    {
      field: 'actExternalAmount',
      label: '实际采购金额',
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: { disabled: true, precision: 2, formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') },
    },
    {
      field: 'evaCostAmount',
      label: '费用预测',
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请输入费用预测' : '',
        precision: 2,
        formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
      }),
    },
    {
      field: 'actCostAmount',
      label: '实际费用',
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: { disabled: true, precision: 2, formatter: (value: number) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') },
    },
    {
      field: 'autoManhours',
      label: '累计工时',
      component: 'InputNumber',
      colProps: { span: 6 },
      componentProps: { disabled: true, precision: 1, formatter: (value: number) => `${value}小时` },
    },
    {
      field: 'autoRatioLevel',
      label: '毛利率区间',
      component: 'Input',
      colProps: { span: 6 },
      componentProps: { disabled: true },
    },

    // 备注信息分组
    {
      field: 'groupTitleNote',
      label: '',
      component: 'GroupTitle',
      componentProps: { content: '备注信息' },
      colProps: { span: 24 },
    },
    {
      field: 'note',
      label: '备注',
      component: 'Textarea',
      colProps: { span: 24 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请输入备注信息' : '',
        rows: 4,
      }),
    },
    {
      field: 'continueDesc',
      label: '续签说明',
      component: 'Textarea',
      colProps: { span: 24 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请输入续签说明' : '',
        rows: 3,
      }),
    },
    {
      field: 'accdocPath',
      label: '验收文档路径',
      component: 'Input',
      colProps: { span: 24 },
      componentProps: ({ formModel }) => ({
        disabled: !editMode.value,
        placeholder: editMode.value ? '请输入验收文档路径' : '',
      }),
    },
  ];

  // 创建统一的表单实例
  const [registerForm, { validate, setFieldsValue, updateSchema }] = useForm({
    labelWidth: 120,
    schemas: formSchemas,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });

  // 设置表单数据
  const setAllFormData = async (contract: any) => {
    if (!contract) return;

    try {
      // 等待表单就绪
      await new Promise(resolve => setTimeout(resolve, 300));

      // 直接设置完整的合同数据到统一表单
      await setFieldsValue(contract);
    } catch (error) {
      console.error('设置表单数据失败:', error);
    }
  };

  // 加载合同信息
  const loadContractInfo = async (showSuccessMessage = true) => {
    if (!contractId.value) {
      console.log('⚠️ 合同ID为空，无法加载合同信息');
      return;
    }

    // 防止重复加载
    if (isLoadingContract.value) {
      console.log('⚠️ [合同信息] 正在加载中，跳过重复请求');
      return;
    }

    isLoadingContract.value = true;
    loading.value = true;
    try {
      console.log('🔄 [合同信息] 开始从后台重新加载合同数据，合同ID:', contractId.value);

      // 从后台重新获取最新的合同信息
      const response = await getContractInfo(contractId.value);

      if ((response as any).code === 200 || (response as any).data) {
        const latestContractInfo = (response as any).data || response;
        console.log('✅ [合同信息] 从后台获取最新合同数据成功:', latestContractInfo);

        // 更新注入的合同信息（如果可能的话）
        if (contractInfo.value && typeof contractInfo.value === 'object') {
          Object.assign(contractInfo.value, latestContractInfo);
        }

        // 将最新数据设置到表单中
        await setAllFormData(latestContractInfo);

        console.log('✅ [合同信息] 合同信息刷新完成');
        if (showSuccessMessage) {
          createMessage.success('合同信息已刷新');
        }
      } else {
        throw new Error((response as any).msg || '获取合同信息失败');
      }
    } catch (error) {
      console.error('❌ [合同信息] 加载合同信息失败:', error);
      createMessage.error('加载合同信息失败');
    } finally {
      loading.value = false;
      isLoadingContract.value = false;
    }
  };

  // 编辑合同
  const handleEdit = async () => {
    // 确保字典数据已加载
    if (!contractStatusOptions.value || contractStatusOptions.value.length === 0) {
      await loadContractStatusDict();
    }

    editMode.value = true;

    // 切换到编辑模式后，为编辑表单设置数据
    if (contractInfo.value) {
      await nextTick();
      await setAllFormData(contractInfo.value);
    }
  };

  // 保存合同信息
  const handleSave = async () => {
    try {
      saveLoading.value = true;

      // 验证表单
      const formData = await validate();

      // 构建提交数据
      const submitData: ContractFormModel = {
        ...formData,
        projBaseId: projectId.value,
        // 保留一些只读字段的原始值
        ysAmount: (contractInfo.value as any)?.ysAmount || 0,
        yearYsAmount: (contractInfo.value as any)?.yearYsAmount || 0,
        moneyStatus: (contractInfo.value as any)?.moneyStatus || '',
        typeStatus: (contractInfo.value as any)?.typeStatus || '',
        signYear: formData.signDate ? new Date(formData.signDate).getFullYear() : undefined,
        outYfEbAmount: (contractInfo.value as any)?.outYfEbAmount || undefined,
        outYfJfAmount: (contractInfo.value as any)?.outYfJfAmount || undefined,
      };

      // 调用合同更新API
      await updateContract(contractId.value, submitData);

      createMessage.success('合同信息更新成功');
      editMode.value = false;

      // 重新加载数据，不显示额外的成功消息
      await loadContractInfo(false);
    } catch (error) {
      createMessage.error('保存合同信息失败');
    } finally {
      saveLoading.value = false;
    }
  };

  // 取消编辑
  const handleCancel = () => {
    editMode.value = false;
    loadContractInfo(false); // 重新加载数据，不显示成功消息
  };

  // 创建合同
  const handleCreateContract = () => {
    createMessage.info('创建合同功能开发中...');
  };

  // 刷新
  const handleRefresh = () => {
    loadContractInfo(true); // 手动刷新，显示成功消息
  };

  // 监听合同信息变化
  watch(
    contractInfo,
    newContractInfo => {
      if (newContractInfo) {
        // 延迟加载，确保表单组件已准备就绪
        setTimeout(() => {
          loadContractInfo(false); // 自动加载，不显示成功消息
        }, 300);
      } else {
        // 合同信息清空时，退出编辑模式
        editMode.value = false;
      }
    },
    { immediate: false, deep: true },
  );

  // 监听编辑模式变化，更新状态字段配置
  watch(
    editMode,
    async () => {
      await nextTick();
      updateStatusFields();
    },
    { immediate: true },
  );

  onMounted(async () => {
    // 首先加载数据字典
    await loadContractStatusDict();
    await nextTick();

    // 延迟加载，确保所有表单组件都已渲染
    setTimeout(() => {
      if (contractInfo.value) {
        loadContractInfo(false); // 初始加载，不显示成功消息
      }
    }, 500);
  });

  onActivated(async () => {
    // 如果数据字典还没加载，先加载
    if (!contractStatusOptions.value || contractStatusOptions.value.length === 0) {
      await loadContractStatusDict();
      await nextTick();
    }

    // 延迟加载，确保所有表单组件都已渲染
    setTimeout(() => {
      if (contractInfo.value) {
        loadContractInfo(false); // 激活时加载，不显示成功消息
      }
    }, 500);
  });
</script>

<style lang="less" scoped>
  .contract-info-management {
    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 16px;
      background: #fff;
      border-radius: 6px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    }

    .page-content {
      .contract-detail {
        .ant-card {
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
        }
      }

      .empty-state {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 400px;
        background: #fff;
        border-radius: 6px;
      }
    }
  }
</style>
