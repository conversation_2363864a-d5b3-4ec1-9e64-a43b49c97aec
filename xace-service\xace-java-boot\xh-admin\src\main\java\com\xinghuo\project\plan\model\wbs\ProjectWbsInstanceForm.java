package com.xinghuo.project.plan.model.wbs;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目实际WBS计划表单对象
 * 用于WBS计划的创建和更新
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@Schema(description = "项目实际WBS计划表单对象")
public class ProjectWbsInstanceForm {

    /**
     * 主键ID（更新时必填）
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 所属的实际项目ID
     */
    @NotBlank(message = "项目ID不能为空")
    @Schema(description = "所属的实际项目ID")
    private String projectId;

    /**
     * 源自哪个项目模板WBS明细ID
     */
    @Schema(description = "源项目模板WBS明细ID")
    private String sourceSchemaWbsId;

    /**
     * 父级节点ID
     */
    @Schema(description = "父级节点ID")
    private String parentId;

    /**
     * WBS编码
     */
    @Schema(description = "WBS编码")
    private String wbsCode;

    /**
     * 活动/工作包名称
     */
    @NotBlank(message = "活动/工作包名称不能为空")
    @Schema(description = "活动/工作包名称")
    private String name;

    /**
     * 显示顺序
     */
    @Schema(description = "显示顺序")
    private Integer seqNo;

    /**
     * 层级深度
     */
    @NotNull(message = "层级深度不能为空")
    @Schema(description = "层级深度")
    private Integer level;

    /**
     * 节点类型 (1:活动/任务, 3:工作包/摘要)
     */
    @NotNull(message = "节点类型不能为空")
    @Schema(description = "节点类型")
    private Integer nodeType;

    /**
     * 是否是里程碑
     */
    @Schema(description = "是否是里程碑")
    private Integer isMilestone;

    /**
     * 计划开始日期
     */
    @Schema(description = "计划开始日期")
    private Date planStartDate;

    /**
     * 计划结束日期
     */
    @Schema(description = "计划结束日期")
    private Date planEndDate;

    /**
     * 计划工期 (天)
     */
    @Schema(description = "计划工期（天）")
    private BigDecimal planDuration;

    /**
     * 计划工时 (小时)
     */
    @Schema(description = "计划工时（小时）")
    private BigDecimal planHour;

    /**
     * 约束类型ID
     */
    @Schema(description = "约束类型ID")
    private String constraintTypeId;

    /**
     * 约束日期
     */
    @Schema(description = "约束日期")
    private Date constraintDate;

    /**
     * 实际开始日期
     */
    @Schema(description = "实际开始日期")
    private Date actualStartDate;

    /**
     * 实际结束日期
     */
    @Schema(description = "实际结束日期")
    private Date actualEndDate;

    /**
     * 实际发生工时 (由工时表汇总)
     */
    @Schema(description = "实际发生工时（小时）")
    private BigDecimal actualHour;

    /**
     * 完成百分比 (0-100)
     */
    @Schema(description = "完成百分比")
    private Integer progress;

    /**
     * 任务状态ID
     */
    @NotBlank(message = "任务状态ID不能为空")
    @Schema(description = "任务状态ID")
    private String statusId;

    /**
     * 责任人ID
     */
    @Schema(description = "责任人ID")
    private String responseUserId;

    /**
     * 完成方式ID
     */
    @Schema(description = "完成方式ID")
    private String completeTypeId;

    /**
     * 确认人ID
     */
    @Schema(description = "确认人ID")
    private String confirmUserId;

    /**
     * 前置任务ID列表
     */
    @Schema(description = "前置任务ID列表")
    private String predecessors;
}