# 付款合同列表显示问题调试清单

## 🔍 已修复的问题

### 1. **数据结构映射问题**
- ✅ 修复了 `total` 字段获取路径：`data.pagination.total` 而不是 `data.total`
- ✅ 修复了供应商字段名：`supplierName` 而不是 `suppilerName`
- ✅ 修复了状态值映射：支持数字状态值 `"1"` = `"执行中"`

### 2. **API响应数据结构**
```json
{
  "code": 200,
  "data": {
    "list": [...],           // ✅ 数据列表
    "pagination": {
      "currentPage": 1,
      "pageSize": 20,
      "total": 1             // ✅ 总记录数
    }
  }
}
```

### 3. **字段映射修复**
| 表格列 | API字段 | 状态 |
|--------|---------|------|
| 采购合同名称 | `name` | ✅ |
| 采购合同编号 | `cno` | ✅ |
| 供应商 | `supplierName` | ✅ 修复 |
| 负责人 | `ownName` | ✅ |
| 合同金额 | `amount` | ✅ |
| 已付金额 | `yfAmount` | ✅ |
| 合同状态 | `status` | ✅ 修复 |
| 签订日期 | `signDate` | ✅ |

## 🧪 调试步骤

### 1. **检查控制台日志**
访问页面后，在浏览器控制台中查看以下日志：

```
🔍 [分页加载] 开始加载采购合同数据
🔍 [分页加载] 合同ID: 603517658549193157
🔍 [分页加载] 分页参数: {page: 1, pageSize: 10}
🔍 [分页加载] 最终查询参数: {currentPage: 1, pageSize: 10, keyword: ""}
✅ [分页加载] 采购合同加载成功: 1 条记录，总计: 1
🔍 [调试] 完整响应数据: {...}
🔍 [调试] 数据列表: [...]
🔍 [调试] 返回给 BasicTable 的最终数据: {items: [...], total: 1}
```

### 2. **验证数据内容**
确认控制台中显示的数据包含以下字段：
- ✅ `name`: "电源采购订单"
- ✅ `cno`: "CG20250045"
- ✅ `supplierName`: "深圳市天地和网络有限公司"
- ✅ `ownName`: "邬柏青"
- ✅ `amount`: 7200
- ✅ `yfAmount`: 7200
- ✅ `status`: "1"
- ✅ `signDate`: "2024-12-26"

### 3. **检查表格渲染**
如果数据正确但表格不显示，检查：

#### A. BasicTable 配置
```typescript
const [registerTable, { reload: reloadTable }] = useTable({
  title: '付款合同列表',
  api: loadPaycontractData,        // ✅ 数据加载函数
  columns: getTableColumns(),      // ✅ 列配置
  useSearchForm: true,             // ✅ 启用搜索
  // ...
});
```

#### B. 列配置检查
```typescript
const getTableColumns = () => [
  {
    title: '采购合同名称',
    dataIndex: 'name',           // ✅ 对应 API 字段
    width: 200,
  },
  {
    title: '供应商',
    dataIndex: 'supplierName',   // ✅ 修复后的字段名
    width: 150,
  },
  // ...
];
```

#### C. 状态显示检查
```typescript
const getPayStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    '1': '执行中',               // ✅ 支持数字状态
    '2': '已完成',
    '3': '已取消',
    '0': '草稿',
    // ...
  };
  return textMap[status] || `未知(${status})`;
};
```

## 🚨 可能的问题和解决方案

### 问题1: 表格完全不显示
**可能原因**：
- BasicTable 注册失败
- API 调用失败
- 数据格式不正确

**解决方案**：
1. 检查控制台是否有错误信息
2. 确认 `registerTable` 正确绑定到模板
3. 验证 API 响应格式

### 问题2: 表格显示但数据为空
**可能原因**：
- 数据解析错误
- 字段映射不正确
- 分页参数错误

**解决方案**：
1. 检查 `loadPaycontractData` 函数返回的数据
2. 验证 `items` 数组不为空
3. 确认 `total` 值正确

### 问题3: 部分列显示异常
**可能原因**：
- 字段名不匹配
- 自定义渲染函数错误
- 数据类型不正确

**解决方案**：
1. 检查 `dataIndex` 与 API 字段名是否一致
2. 验证 `customRender` 函数逻辑
3. 确认数据类型转换正确

### 问题4: 搜索功能不工作
**可能原因**：
- 搜索表单配置错误
- API 不支持搜索参数
- 参数传递错误

**解决方案**：
1. 确认 `useSearchForm: true`
2. 检查 `formConfig.schemas` 配置
3. 验证 API 接收搜索参数

## 📋 完整测试流程

### 1. **基础功能测试**
1. 访问页面：`http://localhost:3100/#/project/overview/contract/paycontract`
2. 确认表格正常显示
3. 验证数据内容正确

### 2. **分页功能测试**
1. 如果有多页数据，测试页码切换
2. 测试每页条数调整
3. 验证分页信息显示

### 3. **搜索功能测试**
1. 在搜索框输入 "电源"
2. 确认搜索结果正确
3. 清空搜索条件，确认显示全部数据

### 4. **操作功能测试**
1. 点击 "查看" 按钮
2. 点击 "编辑" 按钮
3. 测试 "删除" 功能（谨慎操作）

## 🎯 预期结果

修复后，页面应该显示：

| 采购合同名称 | 采购合同编号 | 供应商 | 负责人 | 合同金额 | 已付金额 | 合同状态 | 签订日期 | 操作 |
|-------------|-------------|--------|--------|----------|----------|----------|----------|------|
| 电源采购订单 | CG20250045 | 深圳市天地和网络有限公司 | 邬柏青 | ¥7,200.00 | ¥7,200.00 | 执行中 | 2024-12-26 | 查看 编辑 删除 |

**分页信息**：第 1-1 条，共 1 条

## 🔧 如果问题仍然存在

请提供以下信息：
1. **控制台完整日志**（特别是调试信息）
2. **Network 标签中的 API 请求和响应**
3. **页面截图**（显示表格状态）
4. **任何错误信息**

这样我可以进一步诊断和修复问题！
