package com.xinghuo.project.schema.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目模板问题清单实体类
 * 对应数据库表：zz_proj_schema_issue
 *
 * 项目模板的问题清单配置，关联标准问题库，用于在项目模板中预设需要检查的问题项。
 * 可以设置某些问题为模板默认必须检查的问题。
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_schema_issue")
public class ProjectSchemaIssueEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 所属项目模板ID (关联 zz_proj_template)
     */
    @TableField("project_template_id")
    private String projectTemplateId;

    /**
     * 关联的标准问题库ID (关联 zz_proj_tpl_issue_library)
     */
    @TableField("library_issue_id")
    private String libraryIssueId;

    /**
     * 是否是模板默认必须检查的问题 (1:是, 0:否)
     */
    @TableField("is_required")
    private Integer isRequired;
}