package com.xinghuo.project.plan.model.phase;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;

/**
 * 项目实际阶段实例表单对象
 * 用于阶段实例的创建和更新
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@Schema(description = "项目实际阶段实例表单对象")
public class ProjectPhaseInstanceForm {

    /**
     * 主键ID（更新时必填）
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 所属的实际项目ID
     */
    @NotBlank(message = "项目ID不能为空")
    @Schema(description = "所属的实际项目ID")
    private String projectId;

    /**
     * 源自哪个项目模板阶段配置ID
     */
    @Schema(description = "源项目模板阶段配置ID")
    private String sourceSchemaPhaseId;

    /**
     * 阶段名称
     */
    @NotBlank(message = "阶段名称不能为空")
    @Schema(description = "阶段名称")
    private String name;

    /**
     * 阶段描述
     */
    @Schema(description = "阶段描述")
    private String description;

    /**
     * 该阶段在此项目中的顺序
     */
    @NotNull(message = "序号不能为空")
    @Schema(description = "序号")
    private Integer seqNo;

    /**
     * 阶段状态ID
     */
    @NotBlank(message = "阶段状态ID不能为空")
    @Schema(description = "阶段状态ID")
    private String statusId;

    /**
     * 计划开始日期
     */
    @Schema(description = "计划开始日期")
    private Date planStartDate;

    /**
     * 计划结束日期
     */
    @Schema(description = "计划结束日期")
    private Date planEndDate;

    /**
     * 实际开始日期
     */
    @Schema(description = "实际开始日期")
    private Date actualStartDate;

    /**
     * 实际结束日期
     */
    @Schema(description = "实际结束日期")
    private Date actualEndDate;

    /**
     * 阶段完成审批状态ID
     */
    @Schema(description = "审批状态ID")
    private String approvalStatusId;

    /**
     * 关联的实际审批流程实例ID
     */
    @Schema(description = "审批流程实例ID")
    private String approvalFlowInstanceId;

    /**
     * 关联的实际检查单实例ID
     */
    @Schema(description = "检查单实例ID")
    private String checklistInstanceId;
}