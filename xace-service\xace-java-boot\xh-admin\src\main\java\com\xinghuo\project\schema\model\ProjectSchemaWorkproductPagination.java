package com.xinghuo.project.schema.model;

import com.xinghuo.common.base.model.Pagination;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目模板交付物计划分页查询参数
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectSchemaWorkproductPagination extends Pagination {

    /**
     * 所属项目模板ID
     */
    private String projectTemplateId;

    /**
     * 交付物名称
     */
    private String name;

    /**
     * 交付物类型ID
     */
    private String typeId;

    /**
     * 交付物子类型ID
     */
    private String subTypeId;

    /**
     * 是否是必需交付物 (1:是, 0:否)
     */
    private Integer isRequired;

    /**
     * 关联的项目模板阶段ID
     */
    private String schemaPhaseId;

    /**
     * 关联的项目模板WBS节点ID
     */
    private String schemaWbsId;

    /**
     * 关键字搜索（名称或描述）
     */
    private String keyword;

    /**
     * 责任角色ID
     */
    private String responseRoleId;

    /**
     * 是否需要评审
     */
    private Integer reviewRequired;
}