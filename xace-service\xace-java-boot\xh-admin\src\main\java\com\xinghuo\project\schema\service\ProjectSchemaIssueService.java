package com.xinghuo.project.schema.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.schema.entity.ProjectSchemaIssueEntity;
import com.xinghuo.project.schema.model.ProjectSchemaIssuePagination;
import com.xinghuo.project.schema.model.vo.ProjectSchemaIssueVO;

import java.util.List;

/**
 * 项目模板问题清单服务接口
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface ProjectSchemaIssueService extends BaseService<ProjectSchemaIssueEntity> {

    /**
     * 获取项目模板问题清单列表
     *
     * @param pagination 分页参数
     * @return 问题清单列表
     */
    List<ProjectSchemaIssueVO> getList(ProjectSchemaIssuePagination pagination);

    /**
     * 根据项目模板ID获取问题清单列表
     *
     * @param projectTemplateId 项目模板ID
     * @return 问题清单列表
     */
    List<ProjectSchemaIssueEntity> getListByProjectTemplateId(String projectTemplateId);

    /**
     * 根据项目模板ID获取必须检查的问题清单
     *
     * @param projectTemplateId 项目模板ID
     * @return 必须检查的问题清单列表
     */
    List<ProjectSchemaIssueEntity> getRequiredListByProjectTemplateId(String projectTemplateId);

    /**
     * 获取问题清单详情
     *
     * @param id 问题清单ID
     * @return 问题清单详情
     */
    ProjectSchemaIssueVO getDetailInfo(String id);

    /**
     * 获取问题清单基本信息
     *
     * @param id 问题清单ID
     * @return 问题清单信息
     */
    ProjectSchemaIssueEntity getInfo(String id);

    /**
     * 创建项目模板问题清单
     *
     * @param issueVO 问题清单信息
     * @return 问题清单ID
     */
    String create(ProjectSchemaIssueVO issueVO);

    /**
     * 更新项目模板问题清单
     *
     * @param id 问题清单ID
     * @param issueVO 问题清单信息
     */
    void update(String id, ProjectSchemaIssueVO issueVO);

    /**
     * 删除项目模板问题清单
     *
     * @param id 问题清单ID
     */
    void delete(String id);

    /**
     * 批量删除项目模板问题清单
     *
     * @param ids 问题清单ID列表
     */
    void batchDelete(List<String> ids);

    /**
     * 更新必须检查状态
     *
     * @param id 问题清单ID
     * @param isRequired 是否必须检查
     */
    void updateRequired(String id, Integer isRequired);

    /**
     * 批量更新必须检查状态
     *
     * @param ids 问题清单ID列表
     * @param isRequired 是否必须检查
     */
    void batchUpdateRequired(List<String> ids, Integer isRequired);

    /**
     * 检查问题是否已存在
     *
     * @param projectTemplateId 项目模板ID
     * @param libraryIssueId 标准问题库ID
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByLibraryIssueId(String projectTemplateId, String libraryIssueId, String excludeId);

    /**
     * 从标准问题库导入
     *
     * @param projectTemplateId 项目模板ID
     * @param libraryIssueIds 标准问题库ID列表
     */
    void importFromLibrary(String projectTemplateId, List<String> libraryIssueIds);

    /**
     * 复制问题清单
     *
     * @param sourceProjectTemplateId 源项目模板ID
     * @param targetProjectTemplateId 目标项目模板ID
     */
    void copyIssues(String sourceProjectTemplateId, String targetProjectTemplateId);

    /**
     * 根据项目模板ID删除所有问题清单
     *
     * @param projectTemplateId 项目模板ID
     */
    void deleteByProjectTemplateId(String projectTemplateId);

    /**
     * 根据标准问题库ID删除问题清单
     *
     * @param libraryIssueId 标准问题库ID
     */
    void deleteByLibraryIssueId(String libraryIssueId);

    /**
     * 批量添加标准问题库到项目模板
     *
     * @param projectTemplateId 项目模板ID
     * @param libraryIssueIds 标准问题库ID列表
     * @param isRequired 是否必须检查
     */
    void batchAddIssues(String projectTemplateId, List<String> libraryIssueIds, Integer isRequired);

    /**
     * 同步标准问题库变更
     *
     * @param libraryIssueId 标准问题库ID
     */
    void syncLibraryIssueChanges(String libraryIssueId);

    /**
     * 根据问题类别获取问题清单
     *
     * @param projectTemplateId 项目模板ID
     * @param issueCategory 问题类别
     * @return 问题清单列表
     */
    List<ProjectSchemaIssueEntity> getListByCategory(String projectTemplateId, String issueCategory);

    /**
     * 根据问题级别获取问题清单
     *
     * @param projectTemplateId 项目模板ID
     * @param issueLevel 问题级别
     * @return 问题清单列表
     */
    List<ProjectSchemaIssueEntity> getListByLevel(String projectTemplateId, String issueLevel);
}