package com.xinghuo.project.plan.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.project.plan.entity.ProjectWbsInstanceEntity;
import com.xinghuo.project.plan.model.wbs.ProjectWbsInstanceForm;
import com.xinghuo.project.plan.model.wbs.ProjectWbsInstancePagination;
import com.xinghuo.project.plan.model.wbs.ProjectWbsInstanceVO;
import com.xinghuo.project.plan.service.ProjectWbsInstanceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 项目实际WBS计划管理控制器
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Slf4j
@Tag(name = "项目WBS计划管理", description = "项目实际WBS计划管理相关接口")
@RestController
@RequestMapping("/api/project/plan/projectWbsInstance")
public class ProjectWbsInstanceController {

    @Resource
    private ProjectWbsInstanceService projectWbsInstanceService;

    /**
     * 获取WBS计划列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取WBS计划列表")
    public ActionResult<PageListVO<ProjectWbsInstanceVO>> list(@RequestBody ProjectWbsInstancePagination pagination) {
        try {
            List<ProjectWbsInstanceEntity> list = projectWbsInstanceService.getList(pagination);
            List<ProjectWbsInstanceVO> listVO = BeanCopierUtils.copyList(list, ProjectWbsInstanceVO.class);

            PageListVO<ProjectWbsInstanceVO> vo = new PageListVO<>();
            vo.setList(listVO);
            return ActionResult.success(vo);
        } catch (Exception e) {
            log.error("获取WBS计划列表失败", e);
            return ActionResult.fail("获取WBS计划列表失败");
        }
    }

    /**
     * 根据项目ID获取WBS计划列表
     */
    @GetMapping("/getListByProjectId")
    @Operation(summary = "根据项目ID获取WBS计划列表")
    public ActionResult<List<ProjectWbsInstanceVO>> getListByProjectId(
        @Parameter(description = "项目ID") @RequestParam String projectId) {
        try {
            List<ProjectWbsInstanceEntity> list = projectWbsInstanceService.getListByProjectId(projectId);
            List<ProjectWbsInstanceVO> listVO = BeanCopierUtils.copyList(list, ProjectWbsInstanceVO.class);
            return ActionResult.success(listVO);
        } catch (Exception e) {
            log.error("根据项目ID获取WBS计划列表失败", e);
            return ActionResult.fail("获取WBS计划列表失败");
        }
    }

    /**
     * 根据父级节点ID获取子节点列表
     */
    @GetMapping("/getListByParentId")
    @Operation(summary = "根据父级节点ID获取子节点列表")
    public ActionResult<List<ProjectWbsInstanceVO>> getListByParentId(
        @Parameter(description = "父级节点ID") @RequestParam(required = false) String parentId) {
        try {
            List<ProjectWbsInstanceEntity> list = projectWbsInstanceService.getListByParentId(parentId);
            List<ProjectWbsInstanceVO> listVO = BeanCopierUtils.copyList(list, ProjectWbsInstanceVO.class);
            return ActionResult.success(listVO);
        } catch (Exception e) {
            log.error("根据父级节点ID获取子节点列表失败", e);
            return ActionResult.fail("获取子节点列表失败");
        }
    }

    /**
     * 获取WBS计划详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取WBS计划详情")
    public ActionResult<ProjectWbsInstanceVO> getInfo(
        @Parameter(description = "WBS节点ID") @PathVariable String id) {
        try {
            ProjectWbsInstanceEntity entity = projectWbsInstanceService.getInfo(id);
            if (entity == null) {
                return ActionResult.fail("WBS节点不存在");
            }
            
            ProjectWbsInstanceVO vo = BeanCopierUtils.copy(entity, ProjectWbsInstanceVO.class);
            return ActionResult.success(vo);
        } catch (Exception e) {
            log.error("获取WBS计划详情失败", e);
            return ActionResult.fail("获取WBS计划详情失败");
        }
    }

    /**
     * 创建WBS计划
     */
    @PostMapping
    @Operation(summary = "创建WBS计划")
    public ActionResult<String> create(@Valid @RequestBody ProjectWbsInstanceForm form) {
        try {
            // 检查WBS编码是否已存在
            if (form.getWbsCode() != null && 
                projectWbsInstanceService.isExistByWbsCode(form.getProjectId(), form.getWbsCode(), null)) {
                return ActionResult.fail("WBS编码已存在");
            }

            ProjectWbsInstanceEntity entity = BeanCopierUtils.copy(form, ProjectWbsInstanceEntity.class);
            String id = projectWbsInstanceService.create(entity);
            return ActionResult.success(id);
        } catch (Exception e) {
            log.error("创建WBS计划失败", e);
            return ActionResult.fail("创建WBS计划失败");
        }
    }

    /**
     * 更新WBS计划
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新WBS计划")
    public ActionResult<Void> update(
        @Parameter(description = "WBS节点ID") @PathVariable String id,
        @Valid @RequestBody ProjectWbsInstanceForm form) {
        try {
            // 检查WBS节点是否存在
            ProjectWbsInstanceEntity existing = projectWbsInstanceService.getInfo(id);
            if (existing == null) {
                return ActionResult.fail("WBS节点不存在");
            }

            // 检查WBS编码是否已存在（排除当前记录）
            if (form.getWbsCode() != null && 
                projectWbsInstanceService.isExistByWbsCode(form.getProjectId(), form.getWbsCode(), id)) {
                return ActionResult.fail("WBS编码已存在");
            }

            ProjectWbsInstanceEntity entity = BeanCopierUtils.copy(form, ProjectWbsInstanceEntity.class);
            projectWbsInstanceService.update(id, entity);
            return ActionResult.success();
        } catch (Exception e) {
            log.error("更新WBS计划失败", e);
            return ActionResult.fail("更新WBS计划失败");
        }
    }

    /**
     * 删除WBS计划（级联删除子节点）
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除WBS计划")
    public ActionResult<Void> delete(@Parameter(description = "WBS节点ID") @PathVariable String id) {
        try {
            ProjectWbsInstanceEntity existing = projectWbsInstanceService.getInfo(id);
            if (existing == null) {
                return ActionResult.fail("WBS节点不存在");
            }

            projectWbsInstanceService.delete(id);
            return ActionResult.success();
        } catch (Exception e) {
            log.error("删除WBS计划失败", e);
            return ActionResult.fail("删除WBS计划失败");
        }
    }

    /**
     * 批量删除WBS计划
     */
    @DeleteMapping
    @Operation(summary = "批量删除WBS计划")
    public ActionResult<Void> batchDelete(@RequestBody List<String> ids) {
        try {
            projectWbsInstanceService.batchDelete(ids);
            return ActionResult.success();
        } catch (Exception e) {
            log.error("批量删除WBS计划失败", e);
            return ActionResult.fail("批量删除WBS计划失败");
        }
    }

    /**
     * 获取WBS计划选择列表
     */
    @GetMapping("/getSelectList")
    @Operation(summary = "获取WBS计划选择列表")
    public ActionResult<List<ProjectWbsInstanceVO>> getSelectList(
        @Parameter(description = "项目ID") @RequestParam String projectId,
        @Parameter(description = "关键字") @RequestParam(required = false) String keyword) {
        try {
            List<ProjectWbsInstanceEntity> list = projectWbsInstanceService.getSelectList(projectId, keyword);
            List<ProjectWbsInstanceVO> listVO = BeanCopierUtils.copyList(list, ProjectWbsInstanceVO.class);
            return ActionResult.success(listVO);
        } catch (Exception e) {
            log.error("获取WBS计划选择列表失败", e);
            return ActionResult.fail("获取WBS计划选择列表失败");
        }
    }

    /**
     * 从项目模板创建WBS计划
     */
    @PostMapping("/createFromTemplate")
    @Operation(summary = "从项目模板创建WBS计划")
    public ActionResult<Integer> createFromTemplate(
        @Parameter(description = "项目ID") @RequestParam String projectId,
        @Parameter(description = "项目模板ID") @RequestParam String templateId) {
        try {
            int count = projectWbsInstanceService.createFromTemplate(projectId, templateId);
            return ActionResult.success(count);
        } catch (Exception e) {
            log.error("从项目模板创建WBS计划失败", e);
            return ActionResult.fail("从项目模板创建WBS计划失败");
        }
    }

    /**
     * 获取WBS树形结构
     */
    @GetMapping("/getWbsTree")
    @Operation(summary = "获取WBS树形结构")
    public ActionResult<List<ProjectWbsInstanceVO>> getWbsTree(
        @Parameter(description = "项目ID") @RequestParam String projectId) {
        try {
            List<ProjectWbsInstanceEntity> list = projectWbsInstanceService.getWbsTree(projectId);
            List<ProjectWbsInstanceVO> listVO = BeanCopierUtils.copyList(list, ProjectWbsInstanceVO.class);
            return ActionResult.success(listVO);
        } catch (Exception e) {
            log.error("获取WBS树形结构失败", e);
            return ActionResult.fail("获取WBS树形结构失败");
        }
    }

    /**
     * 更新任务状态
     */
    @PutMapping("/{id}/status")
    @Operation(summary = "更新任务状态")
    public ActionResult<Void> updateStatus(
        @Parameter(description = "WBS节点ID") @PathVariable String id,
        @Parameter(description = "状态ID") @RequestParam String statusId) {
        try {
            projectWbsInstanceService.updateStatus(id, statusId);
            return ActionResult.success();
        } catch (Exception e) {
            log.error("更新任务状态失败", e);
            return ActionResult.fail("更新任务状态失败");
        }
    }

    /**
     * 更新完成进度
     */
    @PutMapping("/{id}/progress")
    @Operation(summary = "更新完成进度")
    public ActionResult<Void> updateProgress(
        @Parameter(description = "WBS节点ID") @PathVariable String id,
        @Parameter(description = "完成百分比") @RequestParam Integer progress) {
        try {
            projectWbsInstanceService.updateProgress(id, progress);
            return ActionResult.success();
        } catch (Exception e) {
            log.error("更新完成进度失败", e);
            return ActionResult.fail("更新完成进度失败");
        }
    }

    /**
     * 开始任务
     */
    @PostMapping("/{id}/start")
    @Operation(summary = "开始任务")
    public ActionResult<Void> startTask(@Parameter(description = "WBS节点ID") @PathVariable String id) {
        try {
            projectWbsInstanceService.startTask(id);
            return ActionResult.success();
        } catch (Exception e) {
            log.error("开始任务失败", e);
            return ActionResult.fail("开始任务失败");
        }
    }

    /**
     * 完成任务
     */
    @PostMapping("/{id}/complete")
    @Operation(summary = "完成任务")
    public ActionResult<Void> completeTask(@Parameter(description = "WBS节点ID") @PathVariable String id) {
        try {
            projectWbsInstanceService.completeTask(id);
            return ActionResult.success();
        } catch (Exception e) {
            log.error("完成任务失败", e);
            return ActionResult.fail("完成任务失败");
        }
    }

    /**
     * 获取任务进度统计
     */
    @GetMapping("/taskProgressStats")
    @Operation(summary = "获取任务进度统计")
    public ActionResult<Map<String, Object>> getTaskProgressStats(
        @Parameter(description = "项目ID") @RequestParam String projectId) {
        try {
            Map<String, Object> stats = projectWbsInstanceService.getTaskProgressStats(projectId);
            return ActionResult.success(stats);
        } catch (Exception e) {
            log.error("获取任务进度统计失败", e);
            return ActionResult.fail("获取任务进度统计失败");
        }
    }

    /**
     * 获取里程碑列表
     */
    @GetMapping("/getMilestoneList")
    @Operation(summary = "获取里程碑列表")
    public ActionResult<List<ProjectWbsInstanceVO>> getMilestoneList(
        @Parameter(description = "项目ID") @RequestParam String projectId) {
        try {
            List<ProjectWbsInstanceEntity> list = projectWbsInstanceService.getMilestoneList(projectId);
            List<ProjectWbsInstanceVO> listVO = BeanCopierUtils.copyList(list, ProjectWbsInstanceVO.class);
            return ActionResult.success(listVO);
        } catch (Exception e) {
            log.error("获取里程碑列表失败", e);
            return ActionResult.fail("获取里程碑列表失败");
        }
    }

    /**
     * 获取关键路径
     */
    @GetMapping("/getCriticalPath")
    @Operation(summary = "获取关键路径")
    public ActionResult<List<ProjectWbsInstanceVO>> getCriticalPath(
        @Parameter(description = "项目ID") @RequestParam String projectId) {
        try {
            List<ProjectWbsInstanceEntity> list = projectWbsInstanceService.getCriticalPath(projectId);
            List<ProjectWbsInstanceVO> listVO = BeanCopierUtils.copyList(list, ProjectWbsInstanceVO.class);
            return ActionResult.success(listVO);
        } catch (Exception e) {
            log.error("获取关键路径失败", e);
            return ActionResult.fail("获取关键路径失败");
        }
    }

    /**
     * 更新前置任务关系
     */
    @PutMapping("/{id}/predecessors")
    @Operation(summary = "更新前置任务关系")
    public ActionResult<Void> updatePredecessors(
        @Parameter(description = "WBS节点ID") @PathVariable String id,
        @Parameter(description = "前置任务ID列表") @RequestParam String predecessors) {
        try {
            projectWbsInstanceService.updatePredecessors(id, predecessors);
            return ActionResult.success();
        } catch (Exception e) {
            log.error("更新前置任务关系失败", e);
            return ActionResult.fail("更新前置任务关系失败");
        }
    }

    /**
     * 调整序号
     */
    @PostMapping("/{id}/adjustSeqNo")
    @Operation(summary = "调整序号")
    public ActionResult<Void> adjustSeqNo(
        @Parameter(description = "WBS节点ID") @PathVariable String id,
        @Parameter(description = "方向(up/down)") @RequestParam String direction) {
        try {
            projectWbsInstanceService.adjustSeqNo(id, direction);
            return ActionResult.success();
        } catch (Exception e) {
            log.error("调整序号失败", e);
            return ActionResult.fail("调整序号失败");
        }
    }
}