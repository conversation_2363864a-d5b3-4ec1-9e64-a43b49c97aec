package com.xinghuo.project.schema.model;

import com.xinghuo.common.base.model.Pagination;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目模板问题清单分页查询参数
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectSchemaIssuePagination extends Pagination {

    /**
     * 所属项目模板ID
     */
    private String projectTemplateId;

    /**
     * 关联的标准问题库ID
     */
    private String libraryIssueId;

    /**
     * 是否是模板默认必须检查的问题 (1:是, 0:否)
     */
    private Integer isRequired;

    /**
     * 关键字搜索
     */
    private String keyword;
}