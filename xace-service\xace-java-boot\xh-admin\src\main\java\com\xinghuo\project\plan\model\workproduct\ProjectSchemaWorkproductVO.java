package com.xinghuo.project.plan.model.workproduct;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 项目模板交付物计划视图对象
 * 用于返回交付物计划列表和详情信息
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@Schema(description = "项目模板交付物计划视图对象")
public class ProjectSchemaWorkproductVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 所属项目模板ID
     */
    @Schema(description = "所属项目模板ID")
    private String projectTemplateId;

    /**
     * 项目模板名称（冗余字段，便于显示）
     */
    @Schema(description = "项目模板名称")
    private String projectTemplateName;

    /**
     * 源自哪个标准交付物库ID
     */
    @Schema(description = "标准交付物库ID")
    private String libraryWorkproductId;

    /**
     * 标准交付物库名称（冗余字段，便于显示）
     */
    @Schema(description = "标准交付物库名称")
    private String libraryWorkproductName;

    /**
     * 交付物名称
     */
    @Schema(description = "交付物名称")
    private String name;

    /**
     * 描述/验收标准
     */
    @Schema(description = "描述/验收标准")
    private String description;

    /**
     * 交付物类型ID
     */
    @Schema(description = "交付物类型ID")
    private String typeId;

    /**
     * 交付物类型名称（冗余字段，便于显示）
     */
    @Schema(description = "交付物类型名称")
    private String typeName;

    /**
     * 交付物子类型ID
     */
    @Schema(description = "交付物子类型ID")
    private String subTypeId;

    /**
     * 交付物子类型名称（冗余字段，便于显示）
     */
    @Schema(description = "交付物子类型名称")
    private String subTypeName;

    /**
     * 显示顺序
     */
    @Schema(description = "显示顺序")
    private Integer seqNo;

    /**
     * 是否是必需交付物 (1:是, 0:否)
     */
    @Schema(description = "是否是必需交付物")
    private Integer isRequired;

    /**
     * 是否是必需交付物名称（冗余字段，便于显示）
     */
    @Schema(description = "是否是必需交付物名称")
    private String isRequiredName;

    /**
     * 责任角色ID
     */
    @Schema(description = "责任角色ID")
    private String responseRoleId;

    /**
     * 责任角色名称（冗余字段，便于显示）
     */
    @Schema(description = "责任角色名称")
    private String responseRoleName;

    /**
     * 是否需要评审
     */
    @Schema(description = "是否需要评审")
    private Integer reviewRequired;

    /**
     * 是否需要评审名称（冗余字段，便于显示）
     */
    @Schema(description = "是否需要评审名称")
    private String reviewRequiredName;

    /**
     * 关联的项目模板阶段ID
     */
    @Schema(description = "关联的项目模板阶段ID")
    private String schemaPhaseId;

    /**
     * 项目模板阶段名称（冗余字段，便于显示）
     */
    @Schema(description = "项目模板阶段名称")
    private String schemaPhaseName;

    /**
     * 关联的项目模板WBS节点ID
     */
    @Schema(description = "关联的项目模板WBS节点ID")
    private String schemaWbsId;

    /**
     * 项目模板WBS节点名称（冗余字段，便于显示）
     */
    @Schema(description = "项目模板WBS节点名称")
    private String schemaWbsName;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private String tenantId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 创建用户ID
     */
    @Schema(description = "创建用户ID")
    private String createdBy;

    /**
     * 创建用户名称 (冗余字段，便于显示)
     */
    @Schema(description = "创建用户名称")
    private String createdByName;

    /**
     * 最后修改时间
     */
    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdatedAt;

    /**
     * 最后修改用户ID
     */
    @Schema(description = "最后修改用户ID")
    private String lastUpdatedBy;

    /**
     * 最后修改用户名称 (冗余字段，便于显示)
     */
    @Schema(description = "最后修改用户名称")
    private String lastUpdatedByName;
}