package com.xinghuo.project.schema.model.vo;

import com.xinghuo.project.schema.entity.ProjectSchemaIssueEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目模板问题清单VO类
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectSchemaIssueVO extends ProjectSchemaIssueEntity {

    /**
     * 项目模板名称（冗余字段，便于显示）
     */
    private String projectTemplateName;

    /**
     * 标准问题库名称（冗余字段，便于显示）
     */
    private String libraryIssueName;

    /**
     * 问题描述（来自问题库）
     */
    private String issueDescription;

    /**
     * 问题级别（来自问题库）
     */
    private String issueLevel;

    /**
     * 问题类别（来自问题库）
     */
    private String issueCategory;

    /**
     * 是否是必须检查的问题显示文本
     */
    private String isRequiredText;

    /**
     * 检查要点（来自问题库）
     */
    private String checkPoints;

    /**
     * 问题影响（来自问题库）
     */
    private String impact;

    /**
     * 解决建议（来自问题库）
     */
    private String solution;

    /**
     * 检查频率（来自问题库）
     */
    private String checkFrequency;
}