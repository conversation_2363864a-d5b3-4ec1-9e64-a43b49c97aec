package com.xinghuo.project.schema.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.project.schema.entity.ProjectSchemaIssueEntity;
import com.xinghuo.project.schema.model.ProjectSchemaIssuePagination;
import com.xinghuo.project.schema.model.vo.ProjectSchemaIssueVO;
import com.xinghuo.project.schema.service.ProjectSchemaIssueService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import cn.dev33.satoken.annotation.SaCheckPermission;

import java.util.List;

/**
 * 项目模板问题清单管理控制器
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Slf4j
@Tag(name = "项目模板问题清单管理", description = "项目模板问题清单管理相关接口")
@RestController
@RequestMapping("/api/project/schema/issue")
public class ProjectSchemaIssueController {

    @Resource
    private ProjectSchemaIssueService projectSchemaIssueService;

    /**
     * 获取项目模板问题清单列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取项目模板问题清单列表")
    @SaCheckPermission("project:schema:issue:view")
    public ActionResult<PageListVO<ProjectSchemaIssueVO>> list(@RequestBody ProjectSchemaIssuePagination pagination) {
        try {
            List<ProjectSchemaIssueVO> list = projectSchemaIssueService.getList(pagination);

            // 对结果进行数据转换和补充
            for (ProjectSchemaIssueVO vo : list) {
                // 是否必须检查字段转换
                if (vo.getIsRequired() != null) {
                    vo.setIsRequiredText(vo.getIsRequired() == 1 ? "是" : "否");
                }

                // TODO: 可以在这里添加其他关联数据的查询和设置
                // 例如：项目模板名称、问题库信息等
            }

            PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
            return ActionResult.page(list, page);
        } catch (Exception e) {
            log.error("获取项目模板问题清单列表失败", e);
            return ActionResult.fail("获取项目模板问题清单列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据项目模板ID获取问题清单列表
     */
    @GetMapping("/getListByTemplateId/{projectTemplateId}")
    @Operation(summary = "根据项目模板ID获取问题清单列表")
    @SaCheckPermission("project:schema:issue:view")
    public ActionResult<List<ProjectSchemaIssueEntity>> getListByProjectTemplateId(
            @Parameter(description = "项目模板ID") @PathVariable String projectTemplateId) {
        try {
            List<ProjectSchemaIssueEntity> list = projectSchemaIssueService.getListByProjectTemplateId(projectTemplateId);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据项目模板ID获取问题清单列表失败", e);
            return ActionResult.fail("获取问题清单列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据项目模板ID获取必须检查的问题清单
     */
    @GetMapping("/getRequiredListByTemplateId/{projectTemplateId}")
    @Operation(summary = "根据项目模板ID获取必须检查的问题清单")
    @SaCheckPermission("project:schema:issue:view")
    public ActionResult<List<ProjectSchemaIssueEntity>> getRequiredListByProjectTemplateId(
            @Parameter(description = "项目模板ID") @PathVariable String projectTemplateId) {
        try {
            List<ProjectSchemaIssueEntity> list = projectSchemaIssueService.getRequiredListByProjectTemplateId(projectTemplateId);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据项目模板ID获取必须检查的问题清单失败", e);
            return ActionResult.fail("获取问题清单失败：" + e.getMessage());
        }
    }

    /**
     * 获取问题清单详情
     */
    @GetMapping("/getInfo/{id}")
    @Operation(summary = "获取问题清单详情")
    @SaCheckPermission("project:schema:issue:view")
    public ActionResult<ProjectSchemaIssueVO> getInfo(@Parameter(description = "问题清单ID") @PathVariable String id) {
        try {
            ProjectSchemaIssueVO issueVO = projectSchemaIssueService.getDetailInfo(id);
            
            // 数据转换
            if (issueVO.getIsRequired() != null) {
                issueVO.setIsRequiredText(issueVO.getIsRequired() == 1 ? "是" : "否");
            }
            
            return ActionResult.success(issueVO);
        } catch (Exception e) {
            log.error("获取问题清单详情失败", e);
            return ActionResult.fail("获取问题清单详情失败：" + e.getMessage());
        }
    }

    /**
     * 创建问题清单
     */
    @PostMapping("/create")
    @Operation(summary = "创建问题清单")
    @SaCheckPermission("project:schema:issue:add")
    public ActionResult<String> create(@Valid @RequestBody ProjectSchemaIssueVO issueVO) {
        try {
            String id = projectSchemaIssueService.create(issueVO);
            return ActionResult.success(id);
        } catch (Exception e) {
            log.error("创建问题清单失败", e);
            return ActionResult.fail("创建问题清单失败：" + e.getMessage());
        }
    }

    /**
     * 更新问题清单
     */
    @PutMapping("/update/{id}")
    @Operation(summary = "更新问题清单")
    @SaCheckPermission("project:schema:issue:edit")
    public ActionResult<Object> update(@Parameter(description = "问题清单ID") @PathVariable String id,
                                      @Valid @RequestBody ProjectSchemaIssueVO issueVO) {
        try {
            projectSchemaIssueService.update(id, issueVO);
            return ActionResult.success("更新成功");
        } catch (Exception e) {
            log.error("更新问题清单失败", e);
            return ActionResult.fail("更新问题清单失败：" + e.getMessage());
        }
    }

    /**
     * 删除问题清单
     */
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除问题清单")
    @SaCheckPermission("project:schema:issue:delete")
    public ActionResult<Object> delete(@Parameter(description = "问题清单ID") @PathVariable String id) {
        try {
            projectSchemaIssueService.delete(id);
            return ActionResult.success("删除成功");
        } catch (Exception e) {
            log.error("删除问题清单失败", e);
            return ActionResult.fail("删除问题清单失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除问题清单
     */
    @DeleteMapping("/batchDelete")
    @Operation(summary = "批量删除问题清单")
    @SaCheckPermission("project:schema:issue:delete")
    public ActionResult<Object> batchDelete(@RequestBody List<String> ids) {
        try {
            projectSchemaIssueService.batchDelete(ids);
            return ActionResult.success("批量删除成功");
        } catch (Exception e) {
            log.error("批量删除问题清单失败", e);
            return ActionResult.fail("批量删除问题清单失败：" + e.getMessage());
        }
    }

    /**
     * 更新必须检查状态
     */
    @PutMapping("/updateRequired/{id}/{isRequired}")
    @Operation(summary = "更新必须检查状态")
    @SaCheckPermission("project:schema:issue:edit")
    public ActionResult<Object> updateRequired(@Parameter(description = "问题清单ID") @PathVariable String id,
                                              @Parameter(description = "是否必须检查") @PathVariable Integer isRequired) {
        try {
            projectSchemaIssueService.updateRequired(id, isRequired);
            return ActionResult.success("更新成功");
        } catch (Exception e) {
            log.error("更新必须检查状态失败", e);
            return ActionResult.fail("更新必须检查状态失败：" + e.getMessage());
        }
    }

    /**
     * 批量更新必须检查状态
     */
    @PutMapping("/batchUpdateRequired/{isRequired}")
    @Operation(summary = "批量更新必须检查状态")
    @SaCheckPermission("project:schema:issue:edit")
    public ActionResult<Object> batchUpdateRequired(@RequestBody List<String> ids,
                                                   @Parameter(description = "是否必须检查") @PathVariable Integer isRequired) {
        try {
            projectSchemaIssueService.batchUpdateRequired(ids, isRequired);
            return ActionResult.success("批量更新成功");
        } catch (Exception e) {
            log.error("批量更新必须检查状态失败", e);
            return ActionResult.fail("批量更新必须检查状态失败：" + e.getMessage());
        }
    }

    /**
     * 从标准问题库导入
     */
    @PostMapping("/importFromLibrary/{projectTemplateId}")
    @Operation(summary = "从标准问题库导入")
    @SaCheckPermission("project:schema:issue:add")
    public ActionResult<Object> importFromLibrary(@Parameter(description = "项目模板ID") @PathVariable String projectTemplateId,
                                                 @RequestBody List<String> libraryIssueIds) {
        try {
            projectSchemaIssueService.importFromLibrary(projectTemplateId, libraryIssueIds);
            return ActionResult.success("导入成功");
        } catch (Exception e) {
            log.error("从标准问题库导入失败", e);
            return ActionResult.fail("导入失败：" + e.getMessage());
        }
    }

    /**
     * 复制问题清单
     */
    @PostMapping("/copyIssues/{sourceProjectTemplateId}/{targetProjectTemplateId}")
    @Operation(summary = "复制问题清单")
    @SaCheckPermission("project:schema:issue:add")
    public ActionResult<Object> copyIssues(@Parameter(description = "源项目模板ID") @PathVariable String sourceProjectTemplateId,
                                         @Parameter(description = "目标项目模板ID") @PathVariable String targetProjectTemplateId) {
        try {
            projectSchemaIssueService.copyIssues(sourceProjectTemplateId, targetProjectTemplateId);
            return ActionResult.success("复制成功");
        } catch (Exception e) {
            log.error("复制问题清单失败", e);
            return ActionResult.fail("复制失败：" + e.getMessage());
        }
    }

    /**
     * 批量添加标准问题库到项目模板
     */
    @PostMapping("/batchAddIssues/{projectTemplateId}/{isRequired}")
    @Operation(summary = "批量添加标准问题库到项目模板")
    @SaCheckPermission("project:schema:issue:add")
    public ActionResult<Object> batchAddIssues(@Parameter(description = "项目模板ID") @PathVariable String projectTemplateId,
                                             @Parameter(description = "是否必须检查") @PathVariable Integer isRequired,
                                             @RequestBody List<String> libraryIssueIds) {
        try {
            projectSchemaIssueService.batchAddIssues(projectTemplateId, libraryIssueIds, isRequired);
            return ActionResult.success("批量添加成功");
        } catch (Exception e) {
            log.error("批量添加标准问题库失败", e);
            return ActionResult.fail("批量添加失败：" + e.getMessage());
        }
    }

    /**
     * 同步标准问题库变更
     */
    @PostMapping("/syncLibraryIssueChanges/{libraryIssueId}")
    @Operation(summary = "同步标准问题库变更")
    @SaCheckPermission("project:schema:issue:edit")
    public ActionResult<Object> syncLibraryIssueChanges(@Parameter(description = "标准问题库ID") @PathVariable String libraryIssueId) {
        try {
            projectSchemaIssueService.syncLibraryIssueChanges(libraryIssueId);
            return ActionResult.success("同步成功");
        } catch (Exception e) {
            log.error("同步标准问题库变更失败", e);
            return ActionResult.fail("同步失败：" + e.getMessage());
        }
    }

    /**
     * 根据问题类别获取问题清单
     */
    @GetMapping("/getListByCategory/{projectTemplateId}/{issueCategory}")
    @Operation(summary = "根据问题类别获取问题清单")
    @SaCheckPermission("project:schema:issue:view")
    public ActionResult<List<ProjectSchemaIssueEntity>> getListByCategory(
            @Parameter(description = "项目模板ID") @PathVariable String projectTemplateId,
            @Parameter(description = "问题类别") @PathVariable String issueCategory) {
        try {
            List<ProjectSchemaIssueEntity> list = projectSchemaIssueService.getListByCategory(projectTemplateId, issueCategory);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据问题类别获取问题清单失败", e);
            return ActionResult.fail("获取问题清单失败：" + e.getMessage());
        }
    }

    /**
     * 根据问题级别获取问题清单
     */
    @GetMapping("/getListByLevel/{projectTemplateId}/{issueLevel}")
    @Operation(summary = "根据问题级别获取问题清单")
    @SaCheckPermission("project:schema:issue:view")
    public ActionResult<List<ProjectSchemaIssueEntity>> getListByLevel(
            @Parameter(description = "项目模板ID") @PathVariable String projectTemplateId,
            @Parameter(description = "问题级别") @PathVariable String issueLevel) {
        try {
            List<ProjectSchemaIssueEntity> list = projectSchemaIssueService.getListByLevel(projectTemplateId, issueLevel);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据问题级别获取问题清单失败", e);
            return ActionResult.fail("获取问题清单失败：" + e.getMessage());
        }
    }
}