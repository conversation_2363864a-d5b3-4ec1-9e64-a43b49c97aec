package com.xinghuo.project.plan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.plan.dao.ProjectWbsInstanceMapper;
import com.xinghuo.project.plan.entity.ProjectWbsInstanceEntity;
import com.xinghuo.project.plan.model.wbs.ProjectWbsInstancePagination;
import com.xinghuo.project.plan.service.ProjectWbsInstanceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 项目实际WBS计划服务实现类
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Slf4j
@Service
public class ProjectWbsInstanceServiceImpl extends BaseServiceImpl<ProjectWbsInstanceMapper, ProjectWbsInstanceEntity> implements ProjectWbsInstanceService {

    @Override
    public List<ProjectWbsInstanceEntity> getList(ProjectWbsInstancePagination pagination) {
        QueryWrapper<ProjectWbsInstanceEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<ProjectWbsInstanceEntity> lambda = queryWrapper.lambda();

        // 根据项目ID查询
        if (StrXhUtil.isNotEmpty(pagination.getProjectId())) {
            lambda.eq(ProjectWbsInstanceEntity::getProjectId, pagination.getProjectId());
        }

        // 根据源模板WBS ID查询
        if (StrXhUtil.isNotEmpty(pagination.getSourceSchemaWbsId())) {
            lambda.eq(ProjectWbsInstanceEntity::getSourceSchemaWbsId, pagination.getSourceSchemaWbsId());
        }

        // 根据父级节点ID查询
        if (StrXhUtil.isNotEmpty(pagination.getParentId())) {
            lambda.eq(ProjectWbsInstanceEntity::getParentId, pagination.getParentId());
        }

        // 根据WBS编码查询
        if (StrXhUtil.isNotEmpty(pagination.getWbsCode())) {
            lambda.like(ProjectWbsInstanceEntity::getWbsCode, pagination.getWbsCode());
        }

        // 层级深度范围查询
        if (pagination.getLevelMin() != null) {
            lambda.ge(ProjectWbsInstanceEntity::getLevel, pagination.getLevelMin());
        }
        if (pagination.getLevelMax() != null) {
            lambda.le(ProjectWbsInstanceEntity::getLevel, pagination.getLevelMax());
        }

        // 根据节点类型查询
        if (pagination.getNodeType() != null) {
            lambda.eq(ProjectWbsInstanceEntity::getNodeType, pagination.getNodeType());
        }

        // 根据是否里程碑查询
        if (pagination.getIsMilestone() != null) {
            lambda.eq(ProjectWbsInstanceEntity::getIsMilestone, pagination.getIsMilestone());
        }

        // 根据任务状态查询
        if (StrXhUtil.isNotEmpty(pagination.getStatusId())) {
            lambda.eq(ProjectWbsInstanceEntity::getStatusId, pagination.getStatusId());
        }

        // 根据责任人查询
        if (StrXhUtil.isNotEmpty(pagination.getResponseUserId())) {
            lambda.eq(ProjectWbsInstanceEntity::getResponseUserId, pagination.getResponseUserId());
        }

        // 关键字搜索（WBS编码、名称）
        if (StrXhUtil.isNotEmpty(pagination.getKeyword())) {
            lambda.and(wrapper -> wrapper
                .like(ProjectWbsInstanceEntity::getWbsCode, pagination.getKeyword())
                .or()
                .like(ProjectWbsInstanceEntity::getName, pagination.getKeyword())
            );
        }

        // 排序
        lambda.orderByAsc(ProjectWbsInstanceEntity::getLevel)
               .orderByAsc(ProjectWbsInstanceEntity::getSeqNo)
               .orderByDesc(ProjectWbsInstanceEntity::getCreatedAt);

        return this.list(lambda);
    }

    @Override
    public List<ProjectWbsInstanceEntity> getListByProjectId(String projectId) {
        LambdaQueryWrapper<ProjectWbsInstanceEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(ProjectWbsInstanceEntity::getProjectId, projectId)
              .orderByAsc(ProjectWbsInstanceEntity::getLevel)
              .orderByAsc(ProjectWbsInstanceEntity::getSeqNo);
        return this.list(lambda);
    }

    @Override
    public List<ProjectWbsInstanceEntity> getListByParentId(String parentId) {
        LambdaQueryWrapper<ProjectWbsInstanceEntity> lambda = new LambdaQueryWrapper<>();
        if (StrXhUtil.isEmpty(parentId)) {
            lambda.isNull(ProjectWbsInstanceEntity::getParentId);
        } else {
            lambda.eq(ProjectWbsInstanceEntity::getParentId, parentId);
        }
        lambda.orderByAsc(ProjectWbsInstanceEntity::getSeqNo);
        return this.list(lambda);
    }

    @Override
    public ProjectWbsInstanceEntity getInfo(String id) {
        return this.getById(id);
    }

    @Override
    @Transactional
    public String create(ProjectWbsInstanceEntity entity) {
        String id = RandomUtil.snowId();
        entity.setId(id);
        
        // 如果没有设置序号，自动获取下一个序号
        if (entity.getSeqNo() == null) {
            entity.setSeqNo(getNextSeqNo(entity.getParentId()));
        }
        
        this.save(entity);
        return id;
    }

    @Override
    @Transactional
    public void update(String id, ProjectWbsInstanceEntity entity) {
        entity.setId(id);
        this.updateById(entity);
    }

    @Override
    @Transactional
    public void delete(String id) {
        // 级联删除子节点
        deleteChildNodes(id);
        // 删除当前节点
        this.removeById(id);
    }

    private void deleteChildNodes(String parentId) {
        List<ProjectWbsInstanceEntity> children = getListByParentId(parentId);
        for (ProjectWbsInstanceEntity child : children) {
            deleteChildNodes(child.getId());
            this.removeById(child.getId());
        }
    }

    @Override
    @Transactional
    public void batchDelete(List<String> ids) {
        for (String id : ids) {
            delete(id);
        }
    }

    @Override
    @Transactional
    public void deleteByProjectId(String projectId) {
        LambdaQueryWrapper<ProjectWbsInstanceEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(ProjectWbsInstanceEntity::getProjectId, projectId);
        this.remove(lambda);
    }

    @Override
    public boolean isExistByWbsCode(String projectId, String wbsCode, String excludeId) {
        LambdaQueryWrapper<ProjectWbsInstanceEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(ProjectWbsInstanceEntity::getProjectId, projectId)
              .eq(ProjectWbsInstanceEntity::getWbsCode, wbsCode);
        
        if (StrXhUtil.isNotEmpty(excludeId)) {
            lambda.ne(ProjectWbsInstanceEntity::getId, excludeId);
        }
        
        return this.count(lambda) > 0;
    }

    @Override
    public ProjectWbsInstanceEntity getBySourceSchemaWbsId(String projectId, String sourceSchemaWbsId) {
        LambdaQueryWrapper<ProjectWbsInstanceEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(ProjectWbsInstanceEntity::getProjectId, projectId)
              .eq(ProjectWbsInstanceEntity::getSourceSchemaWbsId, sourceSchemaWbsId);
        return this.getOne(lambda);
    }

    @Override
    public List<ProjectWbsInstanceEntity> getSelectList(String projectId, String keyword) {
        LambdaQueryWrapper<ProjectWbsInstanceEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(ProjectWbsInstanceEntity::getProjectId, projectId);
        
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.and(wrapper -> wrapper
                .like(ProjectWbsInstanceEntity::getWbsCode, keyword)
                .or()
                .like(ProjectWbsInstanceEntity::getName, keyword)
            );
        }
        
        lambda.orderByAsc(ProjectWbsInstanceEntity::getLevel)
               .orderByAsc(ProjectWbsInstanceEntity::getSeqNo);
        return this.list(lambda);
    }

    @Override
    @Transactional
    public int createFromTemplate(String projectId, String templateId) {
        // TODO: 实现从项目模板创建WBS计划的逻辑
        log.info("从项目模板 {} 为项目 {} 创建WBS计划", templateId, projectId);
        return 0;
    }

    @Override
    @Transactional
    public void batchSave(String projectId, List<ProjectWbsInstanceEntity> wbsInstances) {
        for (ProjectWbsInstanceEntity entity : wbsInstances) {
            entity.setProjectId(projectId);
            if (StrXhUtil.isEmpty(entity.getId())) {
                entity.setId(RandomUtil.snowId());
                this.save(entity);
            } else {
                this.updateById(entity);
            }
        }
    }

    @Override
    @Transactional
    public void updateSeqNo(String id, Integer seqNo) {
        UpdateWrapper<ProjectWbsInstanceEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("F_ID", id)
                    .set("SEQ_NO", seqNo);
        this.update(updateWrapper);
    }

    @Override
    @Transactional
    public void batchUpdateSeqNo(Map<String, Integer> seqNoMap) {
        for (Map.Entry<String, Integer> entry : seqNoMap.entrySet()) {
            updateSeqNo(entry.getKey(), entry.getValue());
        }
    }

    @Override
    @Transactional
    public void updateStatus(String id, String statusId) {
        UpdateWrapper<ProjectWbsInstanceEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("F_ID", id)
                    .set("STATUS_ID", statusId);
        this.update(updateWrapper);
    }

    @Override
    @Transactional
    public void updateProgress(String id, Integer progress) {
        UpdateWrapper<ProjectWbsInstanceEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("F_ID", id)
                    .set("PROGRESS", progress);
        this.update(updateWrapper);
    }

    @Override
    @Transactional
    public void startTask(String id) {
        ProjectWbsInstanceEntity entity = this.getById(id);
        if (entity != null) {
            entity.setActualStartDate(new Date());
            // TODO: 设置为进行中状态
            this.updateById(entity);
        }
    }

    @Override
    @Transactional
    public void completeTask(String id) {
        ProjectWbsInstanceEntity entity = this.getById(id);
        if (entity != null) {
            entity.setActualEndDate(new Date());
            entity.setProgress(100);
            // TODO: 设置为已完成状态
            this.updateById(entity);
        }
    }

    @Override
    public List<ProjectWbsInstanceEntity> getWbsTree(String projectId) {
        return getListByProjectId(projectId);
    }

    @Override
    public Map<String, Object> getTaskProgressStats(String projectId) {
        Map<String, Object> stats = new HashMap<>();
        
        LambdaQueryWrapper<ProjectWbsInstanceEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(ProjectWbsInstanceEntity::getProjectId, projectId);
        
        List<ProjectWbsInstanceEntity> tasks = this.list(lambda);
        
        stats.put("totalTasks", tasks.size());
        
        long completedTasks = tasks.stream()
            .filter(task -> task.getProgress() != null && task.getProgress() >= 100)
            .count();
        stats.put("completedTasks", completedTasks);
        
        // TODO: 添加更多统计信息
        
        return stats;
    }

    @Override
    public List<ProjectWbsInstanceEntity> getMilestoneList(String projectId) {
        LambdaQueryWrapper<ProjectWbsInstanceEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(ProjectWbsInstanceEntity::getProjectId, projectId)
              .eq(ProjectWbsInstanceEntity::getIsMilestone, 1)
              .orderByAsc(ProjectWbsInstanceEntity::getPlanEndDate);
        return this.list(lambda);
    }

    @Override
    public List<ProjectWbsInstanceEntity> getCriticalPath(String projectId) {
        // TODO: 实现关键路径算法
        log.info("计算项目 {} 的关键路径", projectId);
        return getListByProjectId(projectId);
    }

    @Override
    @Transactional
    public void updatePredecessors(String id, String predecessors) {
        UpdateWrapper<ProjectWbsInstanceEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("F_ID", id)
                    .set("PREDECESSORS", predecessors);
        this.update(updateWrapper);
    }

    @Override
    public Integer getNextSeqNo(String parentId) {
        LambdaQueryWrapper<ProjectWbsInstanceEntity> lambda = new LambdaQueryWrapper<>();
        if (StrXhUtil.isEmpty(parentId)) {
            lambda.isNull(ProjectWbsInstanceEntity::getParentId);
        } else {
            lambda.eq(ProjectWbsInstanceEntity::getParentId, parentId);
        }
        lambda.orderByDesc(ProjectWbsInstanceEntity::getSeqNo)
             .last("LIMIT 1");
        
        ProjectWbsInstanceEntity last = this.getOne(lambda);
        return last != null && last.getSeqNo() != null ? last.getSeqNo() + 1 : 1;
    }

    @Override
    @Transactional
    public void adjustSeqNo(String id, String direction) {
        // TODO: 实现序号调整逻辑
        log.info("调整WBS节点 {} 的序号，方向: {}", id, direction);
    }
}