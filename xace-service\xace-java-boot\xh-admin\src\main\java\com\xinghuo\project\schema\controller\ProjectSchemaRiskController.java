package com.xinghuo.project.schema.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.project.schema.entity.ProjectSchemaRiskEntity;
import com.xinghuo.project.schema.model.ProjectSchemaRiskPagination;
import com.xinghuo.project.schema.model.vo.ProjectSchemaRiskVO;
import com.xinghuo.project.schema.service.ProjectSchemaRiskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import cn.dev33.satoken.annotation.SaCheckPermission;

import java.util.List;

/**
 * 项目模板风险清单管理控制器
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Slf4j
@Tag(name = "项目模板风险清单管理", description = "项目模板风险清单管理相关接口")
@RestController
@RequestMapping("/api/project/schema/risk")
public class ProjectSchemaRiskController {

    @Resource
    private ProjectSchemaRiskService projectSchemaRiskService;

    /**
     * 获取项目模板风险清单列表
     */
    @PostMapping("/getList")
    @Operation(summary = "获取项目模板风险清单列表")
    @SaCheckPermission("project:schema:risk:view")
    public ActionResult<PageListVO<ProjectSchemaRiskVO>> list(@RequestBody ProjectSchemaRiskPagination pagination) {
        try {
            List<ProjectSchemaRiskVO> list = projectSchemaRiskService.getList(pagination);

            // 对结果进行数据转换和补充
            for (ProjectSchemaRiskVO vo : list) {
                // 是否必须识别字段转换
                if (vo.getIsRequired() != null) {
                    vo.setIsRequiredText(vo.getIsRequired() == 1 ? "是" : "否");
                }

                // TODO: 可以在这里添加其他关联数据的查询和设置
                // 例如：项目模板名称、风险库信息等
            }

            PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
            return ActionResult.page(list, page);
        } catch (Exception e) {
            log.error("获取项目模板风险清单列表失败", e);
            return ActionResult.fail("获取项目模板风险清单列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据项目模板ID获取风险清单列表
     */
    @GetMapping("/getListByTemplateId/{projectTemplateId}")
    @Operation(summary = "根据项目模板ID获取风险清单列表")
    @SaCheckPermission("project:schema:risk:view")
    public ActionResult<List<ProjectSchemaRiskEntity>> getListByProjectTemplateId(
            @Parameter(description = "项目模板ID") @PathVariable String projectTemplateId) {
        try {
            List<ProjectSchemaRiskEntity> list = projectSchemaRiskService.getListByProjectTemplateId(projectTemplateId);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据项目模板ID获取风险清单列表失败", e);
            return ActionResult.fail("获取风险清单列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据项目模板ID获取必须识别的风险清单
     */
    @GetMapping("/getRequiredListByTemplateId/{projectTemplateId}")
    @Operation(summary = "根据项目模板ID获取必须识别的风险清单")
    @SaCheckPermission("project:schema:risk:view")
    public ActionResult<List<ProjectSchemaRiskEntity>> getRequiredListByProjectTemplateId(
            @Parameter(description = "项目模板ID") @PathVariable String projectTemplateId) {
        try {
            List<ProjectSchemaRiskEntity> list = projectSchemaRiskService.getRequiredListByProjectTemplateId(projectTemplateId);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("根据项目模板ID获取必须识别的风险清单失败", e);
            return ActionResult.fail("获取风险清单失败：" + e.getMessage());
        }
    }

    /**
     * 获取风险清单详情
     */
    @GetMapping("/getInfo/{id}")
    @Operation(summary = "获取风险清单详情")
    @SaCheckPermission("project:schema:risk:view")
    public ActionResult<ProjectSchemaRiskVO> getInfo(@Parameter(description = "风险清单ID") @PathVariable String id) {
        try {
            ProjectSchemaRiskVO riskVO = projectSchemaRiskService.getDetailInfo(id);
            
            // 数据转换
            if (riskVO.getIsRequired() != null) {
                riskVO.setIsRequiredText(riskVO.getIsRequired() == 1 ? "是" : "否");
            }
            
            return ActionResult.success(riskVO);
        } catch (Exception e) {
            log.error("获取风险清单详情失败", e);
            return ActionResult.fail("获取风险清单详情失败：" + e.getMessage());
        }
    }

    /**
     * 创建风险清单
     */
    @PostMapping("/create")
    @Operation(summary = "创建风险清单")
    @SaCheckPermission("project:schema:risk:add")
    public ActionResult<String> create(@Valid @RequestBody ProjectSchemaRiskVO riskVO) {
        try {
            String id = projectSchemaRiskService.create(riskVO);
            return ActionResult.success(id);
        } catch (Exception e) {
            log.error("创建风险清单失败", e);
            return ActionResult.fail("创建风险清单失败：" + e.getMessage());
        }
    }

    /**
     * 更新风险清单
     */
    @PutMapping("/update/{id}")
    @Operation(summary = "更新风险清单")
    @SaCheckPermission("project:schema:risk:edit")
    public ActionResult<Object> update(@Parameter(description = "风险清单ID") @PathVariable String id,
                                      @Valid @RequestBody ProjectSchemaRiskVO riskVO) {
        try {
            projectSchemaRiskService.update(id, riskVO);
            return ActionResult.success("更新成功");
        } catch (Exception e) {
            log.error("更新风险清单失败", e);
            return ActionResult.fail("更新风险清单失败：" + e.getMessage());
        }
    }

    /**
     * 删除风险清单
     */
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除风险清单")
    @SaCheckPermission("project:schema:risk:delete")
    public ActionResult<Object> delete(@Parameter(description = "风险清单ID") @PathVariable String id) {
        try {
            projectSchemaRiskService.delete(id);
            return ActionResult.success("删除成功");
        } catch (Exception e) {
            log.error("删除风险清单失败", e);
            return ActionResult.fail("删除风险清单失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除风险清单
     */
    @DeleteMapping("/batchDelete")
    @Operation(summary = "批量删除风险清单")
    @SaCheckPermission("project:schema:risk:delete")
    public ActionResult<Object> batchDelete(@RequestBody List<String> ids) {
        try {
            projectSchemaRiskService.batchDelete(ids);
            return ActionResult.success("批量删除成功");
        } catch (Exception e) {
            log.error("批量删除风险清单失败", e);
            return ActionResult.fail("批量删除风险清单失败：" + e.getMessage());
        }
    }

    /**
     * 更新必须识别状态
     */
    @PutMapping("/updateRequired/{id}/{isRequired}")
    @Operation(summary = "更新必须识别状态")
    @SaCheckPermission("project:schema:risk:edit")
    public ActionResult<Object> updateRequired(@Parameter(description = "风险清单ID") @PathVariable String id,
                                              @Parameter(description = "是否必须识别") @PathVariable Integer isRequired) {
        try {
            projectSchemaRiskService.updateRequired(id, isRequired);
            return ActionResult.success("更新成功");
        } catch (Exception e) {
            log.error("更新必须识别状态失败", e);
            return ActionResult.fail("更新必须识别状态失败：" + e.getMessage());
        }
    }

    /**
     * 批量更新必须识别状态
     */
    @PutMapping("/batchUpdateRequired/{isRequired}")
    @Operation(summary = "批量更新必须识别状态")
    @SaCheckPermission("project:schema:risk:edit")
    public ActionResult<Object> batchUpdateRequired(@RequestBody List<String> ids,
                                                   @Parameter(description = "是否必须识别") @PathVariable Integer isRequired) {
        try {
            projectSchemaRiskService.batchUpdateRequired(ids, isRequired);
            return ActionResult.success("批量更新成功");
        } catch (Exception e) {
            log.error("批量更新必须识别状态失败", e);
            return ActionResult.fail("批量更新必须识别状态失败：" + e.getMessage());
        }
    }

    /**
     * 从标准风险库导入
     */
    @PostMapping("/importFromLibrary/{projectTemplateId}")
    @Operation(summary = "从标准风险库导入")
    @SaCheckPermission("project:schema:risk:add")
    public ActionResult<Object> importFromLibrary(@Parameter(description = "项目模板ID") @PathVariable String projectTemplateId,
                                                 @RequestBody List<String> libraryRiskIds) {
        try {
            projectSchemaRiskService.importFromLibrary(projectTemplateId, libraryRiskIds);
            return ActionResult.success("导入成功");
        } catch (Exception e) {
            log.error("从标准风险库导入失败", e);
            return ActionResult.fail("导入失败：" + e.getMessage());
        }
    }

    /**
     * 复制风险清单
     */
    @PostMapping("/copyRisks/{sourceProjectTemplateId}/{targetProjectTemplateId}")
    @Operation(summary = "复制风险清单")
    @SaCheckPermission("project:schema:risk:add")
    public ActionResult<Object> copyRisks(@Parameter(description = "源项目模板ID") @PathVariable String sourceProjectTemplateId,
                                        @Parameter(description = "目标项目模板ID") @PathVariable String targetProjectTemplateId) {
        try {
            projectSchemaRiskService.copyRisks(sourceProjectTemplateId, targetProjectTemplateId);
            return ActionResult.success("复制成功");
        } catch (Exception e) {
            log.error("复制风险清单失败", e);
            return ActionResult.fail("复制失败：" + e.getMessage());
        }
    }

    /**
     * 批量添加标准风险库到项目模板
     */
    @PostMapping("/batchAddRisks/{projectTemplateId}/{isRequired}")
    @Operation(summary = "批量添加标准风险库到项目模板")
    @SaCheckPermission("project:schema:risk:add")
    public ActionResult<Object> batchAddRisks(@Parameter(description = "项目模板ID") @PathVariable String projectTemplateId,
                                            @Parameter(description = "是否必须识别") @PathVariable Integer isRequired,
                                            @RequestBody List<String> libraryRiskIds) {
        try {
            projectSchemaRiskService.batchAddRisks(projectTemplateId, libraryRiskIds, isRequired);
            return ActionResult.success("批量添加成功");
        } catch (Exception e) {
            log.error("批量添加标准风险库失败", e);
            return ActionResult.fail("批量添加失败：" + e.getMessage());
        }
    }

    /**
     * 同步标准风险库变更
     */
    @PostMapping("/syncLibraryRiskChanges/{libraryRiskId}")
    @Operation(summary = "同步标准风险库变更")
    @SaCheckPermission("project:schema:risk:edit")
    public ActionResult<Object> syncLibraryRiskChanges(@Parameter(description = "标准风险库ID") @PathVariable String libraryRiskId) {
        try {
            projectSchemaRiskService.syncLibraryRiskChanges(libraryRiskId);
            return ActionResult.success("同步成功");
        } catch (Exception e) {
            log.error("同步标准风险库变更失败", e);
            return ActionResult.fail("同步失败：" + e.getMessage());
        }
    }
}