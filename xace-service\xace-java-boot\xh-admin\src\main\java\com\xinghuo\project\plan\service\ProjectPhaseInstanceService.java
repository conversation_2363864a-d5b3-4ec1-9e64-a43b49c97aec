package com.xinghuo.project.plan.service;

import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.project.plan.entity.ProjectPhaseInstanceEntity;
import com.xinghuo.project.plan.model.phase.ProjectPhaseInstancePagination;

import java.util.List;
import java.util.Map;

/**
 * 项目实际阶段实例服务接口
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface ProjectPhaseInstanceService extends BaseService<ProjectPhaseInstanceEntity> {

    /**
     * 获取项目阶段实例列表
     *
     * @param pagination 分页参数
     * @return 阶段实例列表
     */
    List<ProjectPhaseInstanceEntity> getList(ProjectPhaseInstancePagination pagination);

    /**
     * 根据项目ID获取阶段实例列表
     *
     * @param projectId 项目ID
     * @return 阶段实例列表
     */
    List<ProjectPhaseInstanceEntity> getListByProjectId(String projectId);

    /**
     * 获取阶段实例详情
     *
     * @param id 阶段实例ID
     * @return 阶段实例信息
     */
    ProjectPhaseInstanceEntity getInfo(String id);

    /**
     * 创建阶段实例
     *
     * @param entity 阶段实例信息
     * @return 阶段实例ID
     */
    String create(ProjectPhaseInstanceEntity entity);

    /**
     * 更新阶段实例
     *
     * @param id 阶段实例ID
     * @param entity 阶段实例信息
     */
    void update(String id, ProjectPhaseInstanceEntity entity);

    /**
     * 删除阶段实例
     *
     * @param id 阶段实例ID
     */
    void delete(String id);

    /**
     * 批量删除阶段实例
     *
     * @param ids 阶段实例ID列表
     */
    void batchDelete(List<String> ids);

    /**
     * 根据项目ID删除所有阶段实例
     *
     * @param projectId 项目ID
     */
    void deleteByProjectId(String projectId);

    /**
     * 检查阶段在指定项目中是否已存在
     *
     * @param projectId 项目ID
     * @param name 阶段名称
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean isExistByName(String projectId, String name, String excludeId);

    /**
     * 根据源模板阶段ID获取阶段实例
     *
     * @param projectId 项目ID
     * @param sourceSchemaPhaseId 源模板阶段ID
     * @return 阶段实例信息
     */
    ProjectPhaseInstanceEntity getBySourceSchemaPhaseId(String projectId, String sourceSchemaPhaseId);

    /**
     * 获取阶段实例选择列表
     *
     * @param projectId 项目ID
     * @param keyword 关键字
     * @return 阶段实例列表
     */
    List<ProjectPhaseInstanceEntity> getSelectList(String projectId, String keyword);

    /**
     * 从项目模板创建阶段实例
     *
     * @param projectId 项目ID
     * @param templateId 项目模板ID
     * @return 创建的阶段实例数量
     */
    int createFromTemplate(String projectId, String templateId);

    /**
     * 批量保存阶段实例
     *
     * @param projectId 项目ID
     * @param phaseInstances 阶段实例列表
     */
    void batchSave(String projectId, List<ProjectPhaseInstanceEntity> phaseInstances);

    /**
     * 更新序号
     *
     * @param id 阶段实例ID
     * @param seqNo 序号
     */
    void updateSeqNo(String id, Integer seqNo);

    /**
     * 批量更新序号
     *
     * @param seqNoMap 序号映射 (ID -> 序号)
     */
    void batchUpdateSeqNo(Map<String, Integer> seqNoMap);

    /**
     * 更新阶段状态
     *
     * @param id 阶段实例ID
     * @param statusId 状态ID
     */
    void updateStatus(String id, String statusId);

    /**
     * 开始阶段
     *
     * @param id 阶段实例ID
     */
    void startPhase(String id);

    /**
     * 完成阶段
     *
     * @param id 阶段实例ID
     */
    void completePhase(String id);

    /**
     * 获取阶段进度统计
     *
     * @param projectId 项目ID
     * @return 进度统计信息
     */
    Map<String, Object> getPhaseProgressStats(String projectId);

    /**
     * 获取下一个序号
     *
     * @param projectId 项目ID
     * @return 下一个序号
     */
    Integer getNextSeqNo(String projectId);

    /**
     * 调整序号（上移/下移）
     *
     * @param id 阶段实例ID
     * @param direction 方向（up/down）
     */
    void adjustSeqNo(String id, String direction);
}