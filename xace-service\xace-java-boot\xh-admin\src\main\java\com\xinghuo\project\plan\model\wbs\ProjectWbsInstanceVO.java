package com.xinghuo.project.plan.model.wbs;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目实际WBS计划视图对象
 * 用于返回WBS计划列表和详情信息
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@Schema(description = "项目实际WBS计划视图对象")
public class ProjectWbsInstanceVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 所属的实际项目ID
     */
    @Schema(description = "所属的实际项目ID")
    private String projectId;

    /**
     * 项目名称（冗余字段，便于显示）
     */
    @Schema(description = "项目名称")
    private String projectName;

    /**
     * 源自哪个项目模板WBS明细ID
     */
    @Schema(description = "源项目模板WBS明细ID")
    private String sourceSchemaWbsId;

    /**
     * 父级节点ID
     */
    @Schema(description = "父级节点ID")
    private String parentId;

    /**
     * 父级节点名称（冗余字段，便于显示）
     */
    @Schema(description = "父级节点名称")
    private String parentName;

    /**
     * WBS编码
     */
    @Schema(description = "WBS编码")
    private String wbsCode;

    /**
     * 活动/工作包名称
     */
    @Schema(description = "活动/工作包名称")
    private String name;

    /**
     * 显示顺序
     */
    @Schema(description = "显示顺序")
    private Integer seqNo;

    /**
     * 层级深度
     */
    @Schema(description = "层级深度")
    private Integer level;

    /**
     * 节点类型 (1:活动/任务, 3:工作包/摘要)
     */
    @Schema(description = "节点类型")
    private Integer nodeType;

    /**
     * 节点类型名称（冗余字段，便于显示）
     */
    @Schema(description = "节点类型名称")
    private String nodeTypeName;

    /**
     * 是否是里程碑
     */
    @Schema(description = "是否是里程碑")
    private Integer isMilestone;

    /**
     * 是否是里程碑名称（冗余字段，便于显示）
     */
    @Schema(description = "是否是里程碑名称")
    private String isMilestoneName;

    /**
     * 计划开始日期
     */
    @Schema(description = "计划开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planStartDate;

    /**
     * 计划结束日期
     */
    @Schema(description = "计划结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planEndDate;

    /**
     * 计划工期 (天)
     */
    @Schema(description = "计划工期（天）")
    private BigDecimal planDuration;

    /**
     * 计划工时 (小时)
     */
    @Schema(description = "计划工时（小时）")
    private BigDecimal planHour;

    /**
     * 约束类型ID
     */
    @Schema(description = "约束类型ID")
    private String constraintTypeId;

    /**
     * 约束类型名称（冗余字段，便于显示）
     */
    @Schema(description = "约束类型名称")
    private String constraintTypeName;

    /**
     * 约束日期
     */
    @Schema(description = "约束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date constraintDate;

    /**
     * 实际开始日期
     */
    @Schema(description = "实际开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date actualStartDate;

    /**
     * 实际结束日期
     */
    @Schema(description = "实际结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date actualEndDate;

    /**
     * 实际发生工时 (由工时表汇总)
     */
    @Schema(description = "实际发生工时（小时）")
    private BigDecimal actualHour;

    /**
     * 完成百分比 (0-100)
     */
    @Schema(description = "完成百分比")
    private Integer progress;

    /**
     * 任务状态ID
     */
    @Schema(description = "任务状态ID")
    private String statusId;

    /**
     * 任务状态名称（冗余字段，便于显示）
     */
    @Schema(description = "任务状态名称")
    private String statusName;

    /**
     * 责任人ID
     */
    @Schema(description = "责任人ID")
    private String responseUserId;

    /**
     * 责任人名称（冗余字段，便于显示）
     */
    @Schema(description = "责任人名称")
    private String responseUserName;

    /**
     * 完成方式ID
     */
    @Schema(description = "完成方式ID")
    private String completeTypeId;

    /**
     * 完成方式名称（冗余字段，便于显示）
     */
    @Schema(description = "完成方式名称")
    private String completeTypeName;

    /**
     * 确认人ID
     */
    @Schema(description = "确认人ID")
    private String confirmUserId;

    /**
     * 确认人名称（冗余字段，便于显示）
     */
    @Schema(description = "确认人名称")
    private String confirmUserName;

    /**
     * 前置任务ID列表
     */
    @Schema(description = "前置任务ID列表")
    private String predecessors;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private String tenantId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 创建用户ID
     */
    @Schema(description = "创建用户ID")
    private String createdBy;

    /**
     * 创建用户名称 (冗余字段，便于显示)
     */
    @Schema(description = "创建用户名称")
    private String createdByName;

    /**
     * 最后修改时间
     */
    @Schema(description = "最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdatedAt;

    /**
     * 最后修改用户ID
     */
    @Schema(description = "最后修改用户ID")
    private String lastUpdatedBy;

    /**
     * 最后修改用户名称 (冗余字段，便于显示)
     */
    @Schema(description = "最后修改用户名称")
    private String lastUpdatedByName;
}