package com.xinghuo.project.plan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.plan.dao.ProjectSchemaWorkproductMapper;
import com.xinghuo.project.plan.entity.ProjectSchemaWorkproductEntity;
import com.xinghuo.project.plan.model.workproduct.ProjectSchemaWorkproductPagination;
import com.xinghuo.project.plan.service.ProjectSchemaWorkproductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目模板交付物计划服务实现类
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Slf4j
@Service
public class ProjectSchemaWorkproductServiceImpl extends BaseServiceImpl<ProjectSchemaWorkproductMapper, ProjectSchemaWorkproductEntity> implements ProjectSchemaWorkproductService {

    @Override
    public List<ProjectSchemaWorkproductEntity> getList(ProjectSchemaWorkproductPagination pagination) {
        QueryWrapper<ProjectSchemaWorkproductEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<ProjectSchemaWorkproductEntity> lambda = queryWrapper.lambda();

        // 根据项目模板ID查询
        if (StrXhUtil.isNotEmpty(pagination.getProjectTemplateId())) {
            lambda.eq(ProjectSchemaWorkproductEntity::getProjectTemplateId, pagination.getProjectTemplateId());
        }

        // 根据标准交付物库ID查询
        if (StrXhUtil.isNotEmpty(pagination.getLibraryWorkproductId())) {
            lambda.eq(ProjectSchemaWorkproductEntity::getLibraryWorkproductId, pagination.getLibraryWorkproductId());
        }

        // 根据交付物类型ID查询
        if (StrXhUtil.isNotEmpty(pagination.getTypeId())) {
            lambda.eq(ProjectSchemaWorkproductEntity::getTypeId, pagination.getTypeId());
        }

        // 根据交付物子类型ID查询
        if (StrXhUtil.isNotEmpty(pagination.getSubTypeId())) {
            lambda.eq(ProjectSchemaWorkproductEntity::getSubTypeId, pagination.getSubTypeId());
        }

        // 序号范围查询
        if (pagination.getSeqNoMin() != null) {
            lambda.ge(ProjectSchemaWorkproductEntity::getSeqNo, pagination.getSeqNoMin());
        }
        if (pagination.getSeqNoMax() != null) {
            lambda.le(ProjectSchemaWorkproductEntity::getSeqNo, pagination.getSeqNoMax());
        }

        // 根据是否必需查询
        if (pagination.getIsRequired() != null) {
            lambda.eq(ProjectSchemaWorkproductEntity::getIsRequired, pagination.getIsRequired());
        }

        // 根据责任角色ID查询
        if (StrXhUtil.isNotEmpty(pagination.getResponseRoleId())) {
            lambda.eq(ProjectSchemaWorkproductEntity::getResponseRoleId, pagination.getResponseRoleId());
        }

        // 根据是否需要评审查询
        if (pagination.getReviewRequired() != null) {
            lambda.eq(ProjectSchemaWorkproductEntity::getReviewRequired, pagination.getReviewRequired());
        }

        // 根据关联的项目模板阶段ID查询
        if (StrXhUtil.isNotEmpty(pagination.getSchemaPhaseId())) {
            lambda.eq(ProjectSchemaWorkproductEntity::getSchemaPhaseId, pagination.getSchemaPhaseId());
        }

        // 根据关联的项目模板WBS节点ID查询
        if (StrXhUtil.isNotEmpty(pagination.getSchemaWbsId())) {
            lambda.eq(ProjectSchemaWorkproductEntity::getSchemaWbsId, pagination.getSchemaWbsId());
        }

        // 关键字搜索（交付物名称、描述）
        if (StrXhUtil.isNotEmpty(pagination.getKeyword())) {
            lambda.and(wrapper -> wrapper
                .like(ProjectSchemaWorkproductEntity::getName, pagination.getKeyword())
                .or()
                .like(ProjectSchemaWorkproductEntity::getDescription, pagination.getKeyword())
            );
        }

        // 排序
        lambda.orderByAsc(ProjectSchemaWorkproductEntity::getSeqNo)
               .orderByDesc(ProjectSchemaWorkproductEntity::getCreatedAt);

        return this.list(lambda);
    }

    @Override
    public List<ProjectSchemaWorkproductEntity> getListByTemplateId(String projectTemplateId) {
        LambdaQueryWrapper<ProjectSchemaWorkproductEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(ProjectSchemaWorkproductEntity::getProjectTemplateId, projectTemplateId)
              .orderByAsc(ProjectSchemaWorkproductEntity::getSeqNo);
        return this.list(lambda);
    }

    @Override
    public List<ProjectSchemaWorkproductEntity> getListByPhaseId(String schemaPhaseId) {
        LambdaQueryWrapper<ProjectSchemaWorkproductEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(ProjectSchemaWorkproductEntity::getSchemaPhaseId, schemaPhaseId)
              .orderByAsc(ProjectSchemaWorkproductEntity::getSeqNo);
        return this.list(lambda);
    }

    @Override
    public List<ProjectSchemaWorkproductEntity> getListByWbsId(String schemaWbsId) {
        LambdaQueryWrapper<ProjectSchemaWorkproductEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(ProjectSchemaWorkproductEntity::getSchemaWbsId, schemaWbsId)
              .orderByAsc(ProjectSchemaWorkproductEntity::getSeqNo);
        return this.list(lambda);
    }

    @Override
    public ProjectSchemaWorkproductEntity getInfo(String id) {
        return this.getById(id);
    }

    @Override
    @Transactional
    public String create(ProjectSchemaWorkproductEntity entity) {
        String id = RandomUtil.snowId();
        entity.setId(id);
        
        // 如果没有设置序号，自动获取下一个序号
        if (entity.getSeqNo() == null) {
            entity.setSeqNo(getNextSeqNo(entity.getProjectTemplateId()));
        }
        
        this.save(entity);
        return id;
    }

    @Override
    @Transactional
    public void update(String id, ProjectSchemaWorkproductEntity entity) {
        entity.setId(id);
        this.updateById(entity);
    }

    @Override
    @Transactional
    public void delete(String id) {
        this.removeById(id);
    }

    @Override
    @Transactional
    public void batchDelete(List<String> ids) {
        this.removeByIds(ids);
    }

    @Override
    @Transactional
    public void deleteByTemplateId(String projectTemplateId) {
        LambdaQueryWrapper<ProjectSchemaWorkproductEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(ProjectSchemaWorkproductEntity::getProjectTemplateId, projectTemplateId);
        this.remove(lambda);
    }

    @Override
    @Transactional
    public void deleteByPhaseId(String schemaPhaseId) {
        LambdaQueryWrapper<ProjectSchemaWorkproductEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(ProjectSchemaWorkproductEntity::getSchemaPhaseId, schemaPhaseId);
        this.remove(lambda);
    }

    @Override
    @Transactional
    public void deleteByWbsId(String schemaWbsId) {
        LambdaQueryWrapper<ProjectSchemaWorkproductEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(ProjectSchemaWorkproductEntity::getSchemaWbsId, schemaWbsId);
        this.remove(lambda);
    }

    @Override
    public boolean isExistByName(String projectTemplateId, String name, String excludeId) {
        LambdaQueryWrapper<ProjectSchemaWorkproductEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(ProjectSchemaWorkproductEntity::getProjectTemplateId, projectTemplateId)
              .eq(ProjectSchemaWorkproductEntity::getName, name);
        
        if (StrXhUtil.isNotEmpty(excludeId)) {
            lambda.ne(ProjectSchemaWorkproductEntity::getId, excludeId);
        }
        
        return this.count(lambda) > 0;
    }

    @Override
    public ProjectSchemaWorkproductEntity getByLibraryWorkproductId(String projectTemplateId, String libraryWorkproductId) {
        LambdaQueryWrapper<ProjectSchemaWorkproductEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(ProjectSchemaWorkproductEntity::getProjectTemplateId, projectTemplateId)
              .eq(ProjectSchemaWorkproductEntity::getLibraryWorkproductId, libraryWorkproductId);
        return this.getOne(lambda);
    }

    @Override
    public List<ProjectSchemaWorkproductEntity> getSelectList(String projectTemplateId, String keyword) {
        LambdaQueryWrapper<ProjectSchemaWorkproductEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(ProjectSchemaWorkproductEntity::getProjectTemplateId, projectTemplateId);
        
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.like(ProjectSchemaWorkproductEntity::getName, keyword);
        }
        
        lambda.orderByAsc(ProjectSchemaWorkproductEntity::getSeqNo);
        return this.list(lambda);
    }

    @Override
    @Transactional
    public int copyFromLibrary(String projectTemplateId, List<String> libraryWorkproductIds) {
        // TODO: 实现从标准交付物库复制到项目模板的逻辑
        log.info("从标准交付物库复制到项目模板 {}, 交付物库ID列表: {}", projectTemplateId, libraryWorkproductIds);
        return 0;
    }

    @Override
    @Transactional
    public int copyWorkproductConfigs(String sourceTemplateId, String targetTemplateId) {
        // TODO: 实现复制交付物配置的逻辑
        log.info("从源模板 {} 复制交付物配置到目标模板 {}", sourceTemplateId, targetTemplateId);
        return 0;
    }

    @Override
    @Transactional
    public void batchSave(String projectTemplateId, List<ProjectSchemaWorkproductEntity> workproductConfigs) {
        for (ProjectSchemaWorkproductEntity entity : workproductConfigs) {
            entity.setProjectTemplateId(projectTemplateId);
            if (StrXhUtil.isEmpty(entity.getId())) {
                entity.setId(RandomUtil.snowId());
                this.save(entity);
            } else {
                this.updateById(entity);
            }
        }
    }

    @Override
    @Transactional
    public void updateSeqNo(String id, Integer seqNo) {
        UpdateWrapper<ProjectSchemaWorkproductEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("F_ID", id)
                    .set("SEQ_NO", seqNo);
        this.update(updateWrapper);
    }

    @Override
    @Transactional
    public void batchUpdateSeqNo(Map<String, Integer> seqNoMap) {
        for (Map.Entry<String, Integer> entry : seqNoMap.entrySet()) {
            updateSeqNo(entry.getKey(), entry.getValue());
        }
    }

    @Override
    @Transactional
    public void updateIsRequired(String id, Integer isRequired) {
        UpdateWrapper<ProjectSchemaWorkproductEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("F_ID", id)
                    .set("IS_REQUIRED", isRequired);
        this.update(updateWrapper);
    }

    @Override
    @Transactional
    public void updateReviewRequired(String id, Integer reviewRequired) {
        UpdateWrapper<ProjectSchemaWorkproductEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("F_ID", id)
                    .set("REVIEW_REQUIRED", reviewRequired);
        this.update(updateWrapper);
    }

    @Override
    public Map<String, Object> getWorkproductStats(String projectTemplateId) {
        Map<String, Object> stats = new HashMap<>();
        
        LambdaQueryWrapper<ProjectSchemaWorkproductEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(ProjectSchemaWorkproductEntity::getProjectTemplateId, projectTemplateId);
        
        List<ProjectSchemaWorkproductEntity> workproducts = this.list(lambda);
        
        stats.put("totalWorkproducts", workproducts.size());
        
        long requiredCount = workproducts.stream()
            .filter(wp -> wp.getIsRequired() != null && wp.getIsRequired() == 1)
            .count();
        stats.put("requiredWorkproducts", requiredCount);
        
        long reviewRequiredCount = workproducts.stream()
            .filter(wp -> wp.getReviewRequired() != null && wp.getReviewRequired() == 1)
            .count();
        stats.put("reviewRequiredWorkproducts", reviewRequiredCount);
        
        return stats;
    }

    @Override
    public Integer getNextSeqNo(String projectTemplateId) {
        LambdaQueryWrapper<ProjectSchemaWorkproductEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(ProjectSchemaWorkproductEntity::getProjectTemplateId, projectTemplateId)
              .orderByDesc(ProjectSchemaWorkproductEntity::getSeqNo)
              .last("LIMIT 1");
        
        ProjectSchemaWorkproductEntity last = this.getOne(lambda);
        return last != null && last.getSeqNo() != null ? last.getSeqNo() + 1 : 1;
    }

    @Override
    @Transactional
    public void adjustSeqNo(String id, String direction) {
        // TODO: 实现序号调整逻辑
        log.info("调整交付物计划 {} 的序号，方向: {}", id, direction);
    }

    @Override
    public List<ProjectSchemaWorkproductEntity> getRequiredWorkproducts(String projectTemplateId) {
        LambdaQueryWrapper<ProjectSchemaWorkproductEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(ProjectSchemaWorkproductEntity::getProjectTemplateId, projectTemplateId)
              .eq(ProjectSchemaWorkproductEntity::getIsRequired, 1)
              .orderByAsc(ProjectSchemaWorkproductEntity::getSeqNo);
        return this.list(lambda);
    }

    @Override
    public List<ProjectSchemaWorkproductEntity> getReviewRequiredWorkproducts(String projectTemplateId) {
        LambdaQueryWrapper<ProjectSchemaWorkproductEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(ProjectSchemaWorkproductEntity::getProjectTemplateId, projectTemplateId)
              .eq(ProjectSchemaWorkproductEntity::getReviewRequired, 1)
              .orderByAsc(ProjectSchemaWorkproductEntity::getSeqNo);
        return this.list(lambda);
    }
}