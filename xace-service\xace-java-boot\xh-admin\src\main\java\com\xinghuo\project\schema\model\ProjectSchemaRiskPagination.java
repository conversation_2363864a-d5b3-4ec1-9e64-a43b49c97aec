package com.xinghuo.project.schema.model;

import com.xinghuo.common.base.model.Pagination;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目模板风险清单分页查询参数
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectSchemaRiskPagination extends Pagination {

    /**
     * 所属项目模板ID
     */
    private String projectTemplateId;

    /**
     * 关联的标准风险库ID
     */
    private String libraryRiskId;

    /**
     * 是否是模板默认必须识别的风险 (1:是, 0:否)
     */
    private Integer isRequired;

    /**
     * 关键字搜索
     */
    private String keyword;
}