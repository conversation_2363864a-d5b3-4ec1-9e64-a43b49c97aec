package com.xinghuo.project.plan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.plan.dao.ProjectPhaseInstanceMapper;
import com.xinghuo.project.plan.entity.ProjectPhaseInstanceEntity;
import com.xinghuo.project.plan.model.phase.ProjectPhaseInstancePagination;
import com.xinghuo.project.plan.service.ProjectPhaseInstanceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目实际阶段实例服务实现类
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Slf4j
@Service
public class ProjectPhaseInstanceServiceImpl extends BaseServiceImpl<ProjectPhaseInstanceMapper, ProjectPhaseInstanceEntity> implements ProjectPhaseInstanceService {

    @Override
    public List<ProjectPhaseInstanceEntity> getList(ProjectPhaseInstancePagination pagination) {
        QueryWrapper<ProjectPhaseInstanceEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<ProjectPhaseInstanceEntity> lambda = queryWrapper.lambda();

        // 根据项目ID查询
        if (StrXhUtil.isNotEmpty(pagination.getProjectId())) {
            lambda.eq(ProjectPhaseInstanceEntity::getProjectId, pagination.getProjectId());
        }

        // 根据源模板阶段ID查询
        if (StrXhUtil.isNotEmpty(pagination.getSourceSchemaPhaseId())) {
            lambda.eq(ProjectPhaseInstanceEntity::getSourceSchemaPhaseId, pagination.getSourceSchemaPhaseId());
        }

        // 根据阶段状态ID查询
        if (StrXhUtil.isNotEmpty(pagination.getStatusId())) {
            lambda.eq(ProjectPhaseInstanceEntity::getStatusId, pagination.getStatusId());
        }

        // 序号范围查询
        if (pagination.getSeqNoMin() != null) {
            lambda.ge(ProjectPhaseInstanceEntity::getSeqNo, pagination.getSeqNoMin());
        }
        if (pagination.getSeqNoMax() != null) {
            lambda.le(ProjectPhaseInstanceEntity::getSeqNo, pagination.getSeqNoMax());
        }

        // 关键字搜索（阶段名称、描述）
        if (StrXhUtil.isNotEmpty(pagination.getKeyword())) {
            lambda.and(wrapper -> wrapper
                .like(ProjectPhaseInstanceEntity::getName, pagination.getKeyword())
                .or()
                .like(ProjectPhaseInstanceEntity::getDescription, pagination.getKeyword())
            );
        }

        // 排序
        lambda.orderByAsc(ProjectPhaseInstanceEntity::getSeqNo)
               .orderByDesc(ProjectPhaseInstanceEntity::getCreatedAt);

        return this.list(lambda);
    }

    @Override
    public List<ProjectPhaseInstanceEntity> getListByProjectId(String projectId) {
        LambdaQueryWrapper<ProjectPhaseInstanceEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(ProjectPhaseInstanceEntity::getProjectId, projectId)
              .orderByAsc(ProjectPhaseInstanceEntity::getSeqNo);
        return this.list(lambda);
    }

    @Override
    public ProjectPhaseInstanceEntity getInfo(String id) {
        return this.getById(id);
    }

    @Override
    @Transactional
    public String create(ProjectPhaseInstanceEntity entity) {
        String id = RandomUtil.snowId();
        entity.setId(id);
        this.save(entity);
        return id;
    }

    @Override
    @Transactional
    public void update(String id, ProjectPhaseInstanceEntity entity) {
        entity.setId(id);
        this.updateById(entity);
    }

    @Override
    @Transactional
    public void delete(String id) {
        this.removeById(id);
    }

    @Override
    @Transactional
    public void batchDelete(List<String> ids) {
        this.removeByIds(ids);
    }

    @Override
    @Transactional
    public void deleteByProjectId(String projectId) {
        LambdaQueryWrapper<ProjectPhaseInstanceEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(ProjectPhaseInstanceEntity::getProjectId, projectId);
        this.remove(lambda);
    }

    @Override
    public boolean isExistByName(String projectId, String name, String excludeId) {
        LambdaQueryWrapper<ProjectPhaseInstanceEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(ProjectPhaseInstanceEntity::getProjectId, projectId)
              .eq(ProjectPhaseInstanceEntity::getName, name);
        
        if (StrXhUtil.isNotEmpty(excludeId)) {
            lambda.ne(ProjectPhaseInstanceEntity::getId, excludeId);
        }
        
        return this.count(lambda) > 0;
    }

    @Override
    public ProjectPhaseInstanceEntity getBySourceSchemaPhaseId(String projectId, String sourceSchemaPhaseId) {
        LambdaQueryWrapper<ProjectPhaseInstanceEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(ProjectPhaseInstanceEntity::getProjectId, projectId)
              .eq(ProjectPhaseInstanceEntity::getSourceSchemaPhaseId, sourceSchemaPhaseId);
        return this.getOne(lambda);
    }

    @Override
    public List<ProjectPhaseInstanceEntity> getSelectList(String projectId, String keyword) {
        LambdaQueryWrapper<ProjectPhaseInstanceEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(ProjectPhaseInstanceEntity::getProjectId, projectId);
        
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.like(ProjectPhaseInstanceEntity::getName, keyword);
        }
        
        lambda.orderByAsc(ProjectPhaseInstanceEntity::getSeqNo);
        return this.list(lambda);
    }

    @Override
    @Transactional
    public int createFromTemplate(String projectId, String templateId) {
        // TODO: 实现从项目模板创建阶段实例的逻辑
        log.info("从项目模板 {} 为项目 {} 创建阶段实例", templateId, projectId);
        return 0;
    }

    @Override
    @Transactional
    public void batchSave(String projectId, List<ProjectPhaseInstanceEntity> phaseInstances) {
        for (ProjectPhaseInstanceEntity entity : phaseInstances) {
            entity.setProjectId(projectId);
            if (StrXhUtil.isEmpty(entity.getId())) {
                entity.setId(RandomUtil.snowId());
                this.save(entity);
            } else {
                this.updateById(entity);
            }
        }
    }

    @Override
    @Transactional
    public void updateSeqNo(String id, Integer seqNo) {
        UpdateWrapper<ProjectPhaseInstanceEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("F_ID", id)
                    .set("SEQ_NO", seqNo);
        this.update(updateWrapper);
    }

    @Override
    @Transactional
    public void batchUpdateSeqNo(Map<String, Integer> seqNoMap) {
        for (Map.Entry<String, Integer> entry : seqNoMap.entrySet()) {
            updateSeqNo(entry.getKey(), entry.getValue());
        }
    }

    @Override
    @Transactional
    public void updateStatus(String id, String statusId) {
        UpdateWrapper<ProjectPhaseInstanceEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("F_ID", id)
                    .set("STATUS_ID", statusId);
        this.update(updateWrapper);
    }

    @Override
    @Transactional
    public void startPhase(String id) {
        ProjectPhaseInstanceEntity entity = this.getById(id);
        if (entity != null) {
            entity.setActualStartDate(new Date());
            // TODO: 设置为进行中状态
            this.updateById(entity);
        }
    }

    @Override
    @Transactional
    public void completePhase(String id) {
        ProjectPhaseInstanceEntity entity = this.getById(id);
        if (entity != null) {
            entity.setActualEndDate(new Date());
            // TODO: 设置为已完成状态
            this.updateById(entity);
        }
    }

    @Override
    public Map<String, Object> getPhaseProgressStats(String projectId) {
        Map<String, Object> stats = new HashMap<>();
        
        LambdaQueryWrapper<ProjectPhaseInstanceEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(ProjectPhaseInstanceEntity::getProjectId, projectId);
        
        List<ProjectPhaseInstanceEntity> phases = this.list(lambda);
        
        stats.put("totalPhases", phases.size());
        // TODO: 添加更多统计信息
        
        return stats;
    }

    @Override
    public Integer getNextSeqNo(String projectId) {
        LambdaQueryWrapper<ProjectPhaseInstanceEntity> lambda = new LambdaQueryWrapper<>();
        lambda.eq(ProjectPhaseInstanceEntity::getProjectId, projectId)
              .orderByDesc(ProjectPhaseInstanceEntity::getSeqNo)
              .last("LIMIT 1");
        
        ProjectPhaseInstanceEntity last = this.getOne(lambda);
        return last != null && last.getSeqNo() != null ? last.getSeqNo() + 1 : 1;
    }

    @Override
    @Transactional
    public void adjustSeqNo(String id, String direction) {
        // TODO: 实现序号调整逻辑
        log.info("调整阶段实例 {} 的序号，方向: {}", id, direction);
    }
}