package com.xinghuo.project.plan.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;

/**
 * 项目模板的交付物计划实体类
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("zz_proj_schema_workproduct")
public class ProjectSchemaWorkproductEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 所属项目模板ID (关联 zz_proj_template)
     */
    @TableField("PROJECT_TEMPLATE_ID")
    private String projectTemplateId;

    /**
     * 源自哪个标准交付物库ID (用于追溯)
     */
    @TableField("LIBRARY_WORKPRODUCT_ID")
    private String libraryWorkproductId;

    /**
     * 交付物名称
     */
    @TableField("NAME")
    private String name;

    /**
     * 描述/验收标准
     */
    @TableField("DESCRIPTION")
    private String description;

    /**
     * 交付物类型ID
     */
    @TableField("TYPE_ID")
    private String typeId;

    /**
     * 交付物子类型ID
     */
    @TableField("SUB_TYPE_ID")
    private String subTypeId;

    /**
     * 显示顺序
     */
    @TableField("SEQ_NO")
    private Integer seqNo;

    /**
     * 是否是必需交付物 (1:是, 0:否)
     */
    @TableField("IS_REQUIRED")
    private Integer isRequired;

    /**
     * 责任角色ID (可覆盖库中的default_role_id)
     */
    @TableField("RESPONSE_ROLE_ID")
    private String responseRoleId;

    /**
     * 是否需要评审 (可覆盖库中的need_review)
     */
    @TableField("REVIEW_REQUIRED")
    private Integer reviewRequired;

    /**
     * 关联的项目模板阶段ID
     */
    @TableField("SCHEMA_PHASE_ID")
    private String schemaPhaseId;

    /**
     * 关联的项目模板WBS节点ID
     */
    @TableField("SCHEMA_WBS_ID")
    private String schemaWbsId;
}